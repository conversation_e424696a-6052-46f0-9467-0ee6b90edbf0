import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiUrl } from '../../@shared/constants';
import {
  NotesAddEdit,
  NotesEntityName,
  NotesEntityType,
  NotesListingResponse,
  NotesModuleEntityIdKeyName,
  SiteNotes,
  SiteNotesResponse
} from './notes-management.model';
import { map } from 'rxjs/operators';
import { TagListResponseModel } from '../../@shared/components/image-dropbox-gallery/drop-box.model';

@Injectable({
  providedIn: 'root'
})
export class NotesManagementService {
  constructor(private readonly http: HttpClient) {}

  setNotesAddEditItem = (noteItem: NotesAddEdit, entityTypeId: NotesEntityType): NotesAddEdit => {
    return {
      ...noteItem,
      entityId: noteItem[NotesModuleEntityIdKeyName[entityTypeId]],
      entityTypeId,
      entityTypeName: NotesEntityName[entityTypeId]
    };
  };

  setNotesAddEditResponse = (notes: NotesAddEdit[], entityTypeId: NotesEntityType): NotesAddEdit[] => {
    return notes.map(noteItem => this.setNotesAddEditItem(noteItem, entityTypeId));
  };

  // site note
  getAllSiteNotes(entityTypeId: NotesEntityType, options: any): Observable<NotesListingResponse> {
    options = { ...options, [NotesModuleEntityIdKeyName[entityTypeId]]: options.entityId };
    return this.http
      .post<SiteNotesResponse>(`${ApiUrl.GET_ALL_SITE_NOTES}`, options)
      .pipe(map(res => new NotesListingResponse(this.setNotesAddEditResponse(res.siteNotes, entityTypeId), res.totalSiteNotes)));
  }

  getSiteNoteById(entityTypeId: NotesEntityType, noteId: number): Observable<NotesAddEdit> {
    return this.http.get<SiteNotes>(`${ApiUrl.GET_SITE_NOTE}/${noteId}`).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
  }

  deleteSiteNoteById(entityTypeId: NotesEntityType, noteId: number): Observable<NotesAddEdit> {
    return this.http
      .delete<SiteNotes>(`${ApiUrl.DELETE_SITE_NOTE}/${noteId}`)
      .pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
  }

  addUpdateSiteNote(entityTypeId: NotesEntityType, details): Observable<NotesAddEdit> {
    details = { ...details, [NotesModuleEntityIdKeyName[entityTypeId]]: details.entityId };
    if (details.id)
      return this.http.post<SiteNotes>(`${ApiUrl.UPDATE_SITE_NOTE}`, details).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
    return this.http.post<SiteNotes>(`${ApiUrl.ADD_SITE_NOTE}`, details).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
  }

  // Notes - Customer, Portfolio, CM_Tickets
  getAllNotes(entityTypeId: NotesEntityType, options: any): Observable<NotesListingResponse> {
    options = { ...options, [NotesModuleEntityIdKeyName[entityTypeId]]: options.entityId };
    return this.http
      .post<NotesListingResponse>(`${ApiUrl.GET_ALL_NOTES}`, options)
      .pipe(map(res => new NotesListingResponse(this.setNotesAddEditResponse(res.notes, entityTypeId), res.totalNotes)));
  }

  getNoteById(entityTypeId: NotesEntityType, noteId: number): Observable<NotesAddEdit> {
    return this.http.get<NotesAddEdit>(`${ApiUrl.GET_NOTE}/${noteId}`).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
  }

  deleteNoteById(entityTypeId: NotesEntityType, noteId: number): Observable<NotesAddEdit> {
    return this.http.delete<NotesAddEdit>(`${ApiUrl.DELETE_NOTE}/${noteId}`).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
  }

  addUpdateNote(entityTypeId: NotesEntityType, details): Observable<NotesAddEdit> {
    details = { ...details, [NotesModuleEntityIdKeyName[entityTypeId]]: details.entityId };
    if (details.id)
      return this.http.post<NotesAddEdit>(`${ApiUrl.UPDATE_NOTE}`, details).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
    return this.http.post<NotesAddEdit>(`${ApiUrl.ADD_NOTE}`, details).pipe(map(res => this.setNotesAddEditItem(res, entityTypeId)));
  }

  getNoteTagList(queryParamList): Observable<TagListResponseModel[]> {
    const queryParams: HttpParams = new HttpParams({ fromObject: queryParamList });
    return this.http.get<TagListResponseModel[]>(`${ApiUrl.GET_NOTES_TAGS_LIST}?${queryParams.toString()}`);
  }
}
