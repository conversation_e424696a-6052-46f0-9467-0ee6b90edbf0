import { DOCUMENT } from '@angular/common';
import { Component, ElementRef, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NbMediaBreakpointsService, NbMenuService, NbThemeService } from '@nebular/theme';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { MenuItem } from 'primeng/api';
import { Subject, Subscription } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { SiteCheckInService } from '../../../entities/safety/site-checkin/site-checkin.service';
import { AppConstants } from '../../constants/app.constant';
import { ChildMenuDTOS, MenuDTOS } from '../../models/dashboard.model';
import { AllNotificationModal, NotificationActionSummary, User } from '../../models/user.model';
import { CommonService } from '../../services/common.service';
import { NotificationService } from '../../services/notification.service';
import { StorageService } from '../../services/storage.service';
import { ChunkUploadProgressComponent } from '../chunk-upload-progress/chunk-upload-progress.component';
import { NotificationDetailsScreenComponent } from '../notification-details-screen/notification-details-screen.component';
import { SiteCheckinOutComponent } from '../site-checkin-out/site-checkin-out.component';
import { QEAnalyticsGatheringService } from '../../../entities/qe-analytics/services/qe-analytics-gathering.service';
import { QE_MENU_MODULE_ENUM } from '../../enums/qe-menu.enum';

@Component({
  selector: 'qesolar-header',
  styleUrls: ['./header.component.scss'],
  templateUrl: './header.component.html'
})
export class HeaderComponent implements OnInit, OnDestroy {
  private readonly destroy$: Subject<void> = new Subject<void>();
  userPictureOnly = false;
  user: User;
  menu: MenuDTOS[];
  isActiveTab: string;
  isActiveSubTab: string;
  subMenu: ChildMenuDTOS[];
  showSidebar = false;
  themes = [
    {
      value: 'default',
      name: 'Light'
    },
    {
      value: 'dark',
      name: 'Dark'
    },
    {
      value: 'cosmic',
      name: 'Cosmic'
    },
    {
      value: 'corporate',
      name: 'Corporate'
    }
  ];
  currentTheme = 'dark';
  darkThemeActive = true;
  menuItems: MenuItem[] = [];
  modalRef: BsModalRef;
  subscription: Subscription = new Subscription();
  NotificationLoading = false;
  isAnyCheckboxChecked = false;
  @ViewChild('myDropdown') myDropdown: ElementRef;
  dateTimeFormat = AppConstants.dateTimeFormat;
  notificationsList: AllNotificationModal[] = [];
  notificationActions: NotificationActionSummary[] = [];
  selectedUnreadNotifications: AllNotificationModal[] = [];
  total: number;
  pageSize = 10;
  currentPage = 1;
  unreadNotificationCount: number;
  loggedUser;
  profileRandomBgColor = '';
  notificationParams = {
    triggerId: null,
    userId: null,
    showUnreadOnly: false,
    sortBy: 'notificationDate',
    direction: 'desc',
    page: 0,
    itemsCount: 10
  };

  userMenu = [
    { title: 'Profile', link: '/entities/profile', id: 'user-profile' },
    { title: 'Change Password', link: '/entities/profile/change-password', id: 'user-change-password' },
    { title: 'Log out', link: '/auth/login', id: 'user-logout' }
  ];
  environment = environment.env;
  chunkFileUpload = false;

  constructor(
    private readonly menuService: NbMenuService,
    private readonly themeService: NbThemeService,
    private readonly breakpointService: NbMediaBreakpointsService,
    private readonly router: Router,
    private readonly notificationService: NotificationService,
    private readonly route: ActivatedRoute,
    private readonly storageService: StorageService,
    private readonly modalService: BsModalService,
    @Inject(DOCUMENT) private document: Document,
    private readonly commonService: CommonService,
    private readonly siteCheckInService: SiteCheckInService,
    private readonly qeAnalyticsGatheringService: QEAnalyticsGatheringService
  ) {
    this.notificationService.loggedUser$.subscribe(user => {
      this.loggedUser = user;
    });
    this.getUpdatedUnreadCount();
    this.subscription.add(
      this.notificationService.notificationCount$.subscribe({
        next: (data: number) => {
          this.unreadNotificationCount = data;
        },
        error: e => {
          this.NotificationLoading = false;
        }
      })
    );
    this.notificationParams = {
      triggerId: null,
      userId: this.loggedUser.userId,
      showUnreadOnly: true,
      sortBy: 'notificationDate',
      direction: 'desc',
      page: 0,
      itemsCount: 10
    };
    let theme = this.storageService.get('theme');
    theme = theme ? theme : 'dark';
    this.darkThemeActive = theme === 'dark' ? true : false;
    this.changeTheme();
    this.menu = [
      {
        title: 'Site Info',
        id: 'site-info',
        icon: 'assets/images/Sites-Icon-Small.svg',
        hasPermission:
          this.storageService.get('permission').Equipments ||
          this.storageService.get('permission').Portfolio ||
          this.storageService.get('permission').Customer ||
          this.storageService.get('permission').Site ||
          this.storageService.get('permission').SiteDevice ||
          this.storageService.get('permission').SiteDashboard,
        defaultRoute: this.storageService.get('permission').SiteDashboard ? 'Dashboard' : 'Sites',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'site-dashboard',
            route: '/entities/site-dashboard',
            hasPermission: this.storageService.get('permission').SiteDashboard,
            subMenu: []
          },
          {
            title: 'Customers',
            id: 'site-customers',
            route: '/entities/customers',
            hasPermission: this.storageService.get('permission').Customer,
            subMenu: []
          },
          {
            title: 'Portfolios',
            id: 'site-portfolios',
            route: '/entities/portfolios',
            hasPermission: this.storageService.get('permission').Portfolio,
            subMenu: []
          },
          {
            title: 'Sites',
            id: 'sites-listing',
            route: '/entities/sites',
            hasPermission: this.storageService.get('permission').Site,
            subMenu: []
          },
          {
            title: 'Devices',
            id: 'site-devices',
            route: '/entities/site-device',
            hasPermission: this.storageService.get('permission').SiteDevice,
            subMenu: []
          },
          {
            title: 'Equipment',
            id: 'site-quipment',
            route: '/entities/equipment',
            hasPermission: this.storageService.get('permission').Equipments,
            subMenu: []
          }
        ]
      },
      {
        title: 'PM',
        id: 'pm-section',
        icon: 'assets/images/List.svg',
        hasPermission: true,
        defaultRoute: 'Dashboard',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'pm-dashboard',
            route: '/entities/dashboard',
            hasPermission: true,
            subMenu: []
          },
          {
            title: 'Scope',
            id: 'pm-scope',
            route: '/entities/assessments',
            hasPermission: this.storageService.get('permission').Assessment,
            subMenu: []
          },
          {
            title: 'Work Orders',
            id: 'pm-work-orders',
            route: '/entities/workorders',
            hasPermission: this.storageService.get('permission').WO,
            subMenu: []
          },
          {
            title: 'Reports',
            id: 'pm-reports',
            route: '/entities/other-reports',
            hasPermission: this.storageService.get('permission').Report,
            subMenu: []
          },
          {
            title: 'Site Audit',
            id: 'pm-site-audit',
            route: '/entities/site-audit-report',
            hasPermission: this.storageService.get('permission').SiteAuditReport,
            subMenu: []
          },
          {
            title: 'Non-Conformance',
            id: 'pm-non-conformance',
            route: '/entities/non-conformance',
            hasPermission: this.storageService.get('permission').NonConformance,
            subMenu: []
          }
        ]
      },
      {
        title: 'CM',
        id: 'cm-section',
        icon: 'assets/images/Group.svg',
        hasPermission:
          this.storageService.get('permission').Tickets ||
          this.storageService.get('permission').CMReports ||
          this.storageService.get('permission').AuditDispatchReport ||
          this.storageService.get('permission').Exclusions ||
          this.storageService.get('permission').BillingReport ||
          this.storageService.get('permission').PerformanceReport ||
          this.storageService.get('permission').TruckRollReport ||
          this.storageService.get('permission').MapReport ||
          this.storageService.get('permission').CMDashboard,
        defaultRoute: 'All Tickets',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'cm-dashboard',
            route: '/entities/cm-dashboard',
            hasPermission: this.storageService.get('permission').CMDashboard,
            subMenu: []
          },
          {
            title: 'All Tickets',
            id: 'cm-all-tickets',
            route: '/entities/ticket',
            hasPermission: this.storageService.get('permission').Tickets,
            subMenu: []
          },
          {
            title: 'Ticket Audit Report',
            id: 'cm-ticket-audit-report',
            route: '/entities/cm-reports/audit-dispatch',
            hasPermission: this.storageService.get('permission').AuditDispatchReport,
            subMenu: []
          },
          {
            title: 'Exclusion Report',
            id: 'cm-exclusion-report',
            route: '/entities/cm-reports/exclusion',
            hasPermission: this.storageService.get('permission').Exclusions,
            subMenu: []
          },
          {
            title: 'Billing Report',
            id: 'cm-billing-report',
            route: '/entities/cm-reports/billing',
            hasPermission: this.storageService.get('permission').BillingReport,
            subMenu: []
          },
          {
            title: 'Truck Roll Report',
            id: 'cm-truck-roll-report',
            route: '/entities/cm-reports/truckroll',
            hasPermission: this.storageService.get('permission').TruckRollReport,
            subMenu: []
          },
          {
            title: 'Map Report',
            id: 'cm-map-report',
            route: environment.env !== 'prod' ? '/entities/cm-reports/map' : null,
            hasPermission: this.storageService.get('permission').MapReport,
            subMenu: []
          },
          {
            title: 'RMA Report',
            id: 'cm-rma-report',
            route: '/entities/cm-reports/rma-report',
            hasPermission: this.storageService.get('permission').RmaReport,
            subMenu: []
          }
        ]
      },
      {
        title: 'Availability',
        id: 'availability-section',
        icon: 'assets/images/Availability-icon.svg',
        hasPermission:
          this.storageService.get('permission').Availability ||
          this.storageService.get('permission').AvailabilityDataTable ||
          this.storageService.get('permission').AvailabilityExclusion ||
          this.storageService.get('permission').AvailabilityReport,
        defaultRoute: 'Data Table',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'availability-dashboard',
            route: '',
            hasPermission: this.storageService.get('permission').Availability,
            subMenu: []
          },
          {
            title: 'Reports',
            id: 'availability-report',
            route: '/entities/availability/reports',
            hasPermission: this.storageService.get('permission').AvailabilityReport,
            subMenu: []
          },
          {
            title: 'Data Table',
            id: 'availability-data-table',
            route: '/entities/availability/data-table',
            hasPermission: this.storageService.get('permission').AvailabilityDataTable,
            subMenu: []
          },
          {
            title: 'Exclusions',
            id: 'availability-exclusion',
            route: '/entities/availability/exclusions',
            hasPermission: this.storageService.get('permission').AvailabilityExclusion,
            subMenu: []
          }
        ]
      },
      {
        title: 'Performance',
        id: 'performance-section',
        icon: 'assets/images/bar-chart.svg',
        hasPermission:
          this.storageService.get('permission').Performance ||
          this.storageService.get('permission').DataTable ||
          this.storageService.get('permission').PerformanceReports ||
          this.storageService.get('permission').PerformanceDashboard ||
          this.storageService.get('permission').PerformancePowerChart ||
          this.storageService.get('permission').PerformanceOutage,
        defaultRoute: 'Dashboard',
        subMenu: [
          {
            title: 'Dashboard',
            id: 'performance-dashboard',
            route: '/entities/performance/dashboard',
            hasPermission: this.storageService.get('permission').PerformanceDashboard,
            subMenu: []
          },
          {
            title: 'Power Charts',
            id: 'performance-power-charts',
            route: '/entities/performance/power-chart',
            hasPermission: this.storageService.get('permission').PerformancePowerChart,
            subMenu: []
          },
          {
            title: 'Reports',
            id: 'performance-reports',
            route: '/entities/performance/report',
            hasPermission: this.storageService.get('permission').PerformanceReports,
            subMenu: []
          },
          {
            title: 'Data Table',
            id: 'performance-data-table',
            route: '/entities/performance/data-table',
            hasPermission: this.storageService.get('permission').DataTable,
            subMenu: []
          },
          {
            title: 'Alerts',
            id: 'performance-alerts',
            route: '/entities/performance/outage',
            hasPermission: this.storageService.get('permission').PerformanceOutage,
            subMenu: []
          }
        ]
      },
      {
        title: 'Safety',
        id: 'safety-section',
        icon: 'assets/images/Shield.svg',
        hasPermission:
          this.storageService.get('permission').Safety ||
          this.storageService.get('permission').JHA ||
          this.storageService.get('permission').settings ||
          this.storageService.get('permission').SiteCheckIn,
        defaultRoute: 'JHA',
        subMenu: [
          {
            title: 'JHA',
            id: 'safety-jha',
            route: '/entities/safety/jha',
            hasPermission: this.storageService.get('permission').JHA,
            subMenu: []
          },
          {
            title: 'Site Check-In',
            id: 'safety-site-check-in',
            route: '/entities/safety/site-checkin',
            hasPermission: this.storageService.get('permission').SiteCheckIn,
            subMenu: []
          },
          {
            title: 'Site Audit-JHA',
            id: 'safety-site-audit-jha',
            route: '/entities/safety/site-audit-jha',
            hasPermission: this.storageService.get('permission').SiteAuditJHA,
            subMenu: []
          },
          {
            title: 'Settings',
            id: 'settings-section',
            route: '/entities/safety/settings',
            hasPermission: this.storageService.get('permission').Settings,
            subMenu: [
              {
                title: 'General Info',
                id: 'settings-general-info',
                route: '/entities/safety/settings/general-info',
                hasPermission: this.storageService.get('permission').GeneralInfo,
                subMenu: []
              },
              {
                title: 'Work Type',
                id: 'settings-work-type',
                route: '/entities/safety/settings/work-type',
                hasPermission: this.storageService.get('permission').WorkType,
                subMenu: []
              },
              {
                title: 'Work Step',
                id: 'settings-work-step',
                route: '/entities/safety/settings/workstep',
                hasPermission: this.storageService.get('permission').WorkStep,
                subMenu: []
              },
              {
                title: 'Hazard',
                id: 'settings-hazard',
                route: '/entities/safety/settings/hazard',
                hasPermission: this.storageService.get('permission').Hazard,
                subMenu: []
              },
              {
                title: 'Barrier',
                id: 'settings-barrier',
                route: '/entities/safety/settings/barrier',
                hasPermission: this.storageService.get('permission').Barrier,
                subMenu: []
              }
            ]
          }
        ]
      },
      {
        title: 'Operations',
        id: 'operations-section',
        icon: 'assets/images/operation-report.svg',
        hasPermission:
          this.storageService.get('permission').OperationsReports ||
          this.storageService.get('permission').OperationsRegionMapping ||
          this.storageService.get('permission').OperationsServices ||
          this.storageService.get('permission').CustomForms,
        defaultRoute: 'operations',
        subMenu: [
          {
            title: 'Reports',
            id: 'Operations-report',
            route: '/entities/operations/operations-reports',
            hasPermission: this.storageService.get('permission').OperationsReports,
            subMenu: []
          },
          {
            title: 'Region Mapping',
            id: 'region-mapping',
            route: '/entities/operations/region-mapping',
            hasPermission: this.storageService.get('permission').OperationsRegionMapping,
            subMenu: []
          },
          {
            title: 'Services',
            id: 'services',
            route: '/entities/operations/services',
            hasPermission: this.storageService.get('permission').OperationsServices,
            subMenu: []
          },
          {
            title: 'Contracts',
            id: 'contracts',
            route: '/entities/operations/contracts',
            hasPermission: this.storageService.get('permission').contracts,
            subMenu: []
          },
          {
            title: 'Custom Forms',
            id: 'custom-forms',
            route: '/entities/operations/custom-forms',
            hasPermission: this.storageService.get('permission').CustomForms,
            subMenu: []
          }
        ]
      },
      {
        title: 'Admin',
        id: 'admin-section',
        icon: 'assets/images/user.svg',
        hasPermission:
          this.storageService.get('permission').User ||
          this.storageService.get('permission').DataSource ||
          this.storageService.get('permission').apiErrorLog ||
          this.storageService.get('permission').reportSchedule ||
          this.storageService.get('permission').CustomerAPIGateway ||
          this.storageService.get('permission').APIGatewayDashboard ||
          this.storageService.get('permission').EmailLog,
        defaultRoute: 'Users',
        subMenu: [
          {
            title: 'Users',
            id: 'admin-users',
            route: '/entities/admin/users',
            hasPermission: this.storageService.get('permission').User,
            subMenu: []
          },
          {
            title: 'Data Source Mapping',
            id: 'admin-data-source-mapping',
            route: '/entities/admin/dataSource',
            hasPermission: this.storageService.get('permission').DataSource,
            subMenu: []
          },
          {
            title: 'API Error Log',
            id: 'admin-api-error-log',
            route: '/entities/admin/api-error-log',
            hasPermission: this.storageService.get('permission').apiErrorLog,
            subMenu: []
          },
          {
            title: 'Report Scheduler',
            id: 'admin-report-scheduler',
            route: '/entities/admin/report-schedule',
            hasPermission: this.storageService.get('permission').reportSchedule,
            subMenu: []
          },
          {
            title: 'Customer API Gateway',
            id: 'admin-customer-api-gateway',
            route: '/entities/admin/customer-api-gateway',
            hasPermission: this.storageService.get('permission').CustomerAPIGateway,
            subMenu: []
          },
          {
            title: 'API Gateway Dashboard',
            id: 'admin-api-gateway-dashboard',
            route: '/entities/admin/api-gateway-dashboard',
            hasPermission: this.storageService.get('permission').APIGatewayDashboard,
            subMenu: []
          },
          {
            title: 'Re-Fetch Scheduler',
            id: 're-fetch-scheduler',
            route: '/entities/admin/refetch-schedule',
            hasPermission: this.storageService.get('permission').reFetchSchedule,
            subMenu: []
          },
          {
            title: 'Email Log',
            id: 'email-log',
            route: '/entities/admin/email-log',
            hasPermission: this.storageService.get('permission').EmailLog,
            subMenu: []
          },
          {
            title: 'Analytics',
            id: 'analytics',
            route: '/entities/admin/analytics',
            hasPermission: this.storageService.get('permission').Analytics,
            subMenu: []
          }
        ]
      }
    ];
    if (this.router.url === '/entities/profile' || this.router.url === '/entities/profile/change-password') {
      this.isActiveTab = null;
      this.subMenu = [];
    } else {
      const activeTab = this.getActiveTab(this.router.url);
      this.isActiveTab = activeTab.title;
      this.subMenu = activeTab.subMenu;
    }
  }

  ngOnInit() {
    this.currentTheme = this.themeService.currentTheme;
    this.user = this.storageService.get('user');
    const { xl } = this.breakpointService.getBreakpointsMap();
    this.themeService
      .onMediaQueryChange()
      .pipe(
        map(([, currentBreakpoint]) => currentBreakpoint.width < xl),
        takeUntil(this.destroy$)
      )
      .subscribe((isLessThanXl: boolean) => (this.userPictureOnly = isLessThanXl));

    this.themeService
      .onThemeChange()
      .pipe(
        map(({ name }) => name),
        takeUntil(this.destroy$)
      )
      .subscribe(themeName => (this.currentTheme = themeName));
    this.menuService.onItemClick().subscribe(event => {
      this.onItemSelection(event.item.title);
    });
    this.profileRandomBgColor = this.notificationService.getRandomColor();

    this.commonService.isChunkUploadInProgress$.subscribe(uploading => {
      this.chunkFileUpload = uploading;
    });
    this.siteCheckInService.userObjectChangedInStorage$.subscribe(updated => {
      const activeTab = this.getActiveTab(this.router.url);
      this.isActiveTab = activeTab.title;
      this.subMenu = activeTab.subMenu;
      if (updated) {
        this.user = this.storageService.get('user');
      }
    });
  }

  onItemSelection(title) {
    if (title === 'Log out') {
      this.darkThemeActive = true;
      this.changeTheme(false);
      this.notificationService.clearCustomerCache().subscribe(res => {
        this.storageService.clearAll();
        window.location.reload();
      });
    }
  }

  changeTheme(changeval = true) {
    const themeName = this.darkThemeActive ? 'dark' : 'default';
    this.storageService.set('theme', themeName);
    this.themeService.changeTheme(themeName);

    let themeLink = this.document.getElementById('app-theme') as HTMLLinkElement;
    if (themeLink) {
      themeLink.href = (this.darkThemeActive ? 'lara-dark-blue' : 'lara-light-blue') + '.css';
    }
  }

  navigateHome() {
    // if require, need to update the routing url based on the logged in user's role and permissions as needed
    this.router.navigate(['/entities/dashboard']);
    this.isActiveTab = 'PM';
    this.subMenu = this.menu[1].subMenu;
    this.defaultFirstRoute(this.menu[1]);
    // this.menuService.navigateHome();
    return false;
  }

  getFirstLetter(name: string) {
    return name.slice(0, 1);
  }

  getActiveTab(route: string) {
    for (const i of this.menu) {
      for (const j of i.subMenu) {
        if (j.route && (j.route === route || route.includes(j.route))) {
          return i;
        }
      }
    }
    return this.menu[0];
  }

  getClass(route: string) {
    const activeURL = this.router.url;
    return activeURL.includes(route) ? 'activeSubTab' : null;
  }

  defaultFirstRoute(obj: MenuDTOS) {
    if (obj.subMenu && obj.subMenu.length) {
      if (obj.defaultRoute) {
        const menu = obj.subMenu.find(x => x.title === obj.defaultRoute);
        if (menu && menu.hasPermission && menu.route) {
          this.router.navigate([menu.route], { relativeTo: this.route });
          return;
        }
      }
      for (const i of obj.subMenu) {
        if (i.hasPermission && i.route) {
          this.router.navigate([i.route], { relativeTo: this.route });
          break;
        }
      }
    }
  }

  openSidebar() {
    this.menuItems = [];
    this.menu.forEach(menu => {
      const menuItem: MenuItem = {};
      if (menu.hasPermission) {
        menuItem.label = menu.title;
        if (menu.subMenu.length) {
          menuItem.items = [];
          menu.subMenu.forEach(subMenu => {
            const subMenuItem: MenuItem = {};
            if (subMenu.hasPermission) {
              subMenuItem.label = subMenu.title;
              if (subMenu.subMenu.length) {
                subMenuItem.items = [];
                subMenu.subMenu.forEach(subToSubMenu => {
                  const subToSubMenuItem: MenuItem = {};
                  if (subToSubMenu.hasPermission) {
                    subToSubMenuItem.label = subToSubMenu.title;
                    subToSubMenuItem.routerLink = subToSubMenu.route;
                    subToSubMenuItem.command = () => {
                      this.isActiveTab = menu.title;
                      this.subMenu = menu.subMenu;
                      this.showSidebar = !this.showSidebar;
                    };
                  }
                  subMenuItem.items.push(subToSubMenuItem);
                });
              } else {
                subMenuItem.routerLink = subMenu.route;
                subMenuItem.command = () => {
                  this.isActiveTab = menu.title;
                  this.subMenu = menu.subMenu;
                  this.showSidebar = !this.showSidebar;
                };
              }
            }
            menuItem.items.push(subMenuItem);
          });
        }
      }
      this.menuItems.push(menuItem);
    });
    this.userMenu.forEach(item => {
      const menuItem: MenuItem = {};
      menuItem.label = item.title;
      menuItem.routerLink = item.link;
      menuItem.command = () => {
        this.showSidebar = !this.showSidebar;
        this.onItemSelection(menuItem.label);
      };
      this.menuItems.push(menuItem);
    });
    this.showSidebar = !this.showSidebar;
  }

  openNotificationPopUp() {
    this.notificationParams.showUnreadOnly = true;
    const getListWithDefaultParam = {
      ...this.notificationParams,
      page: 0,
      itemsCount: 10
    };
    this.getAllNotification(getListWithDefaultParam);
  }

  refreshListOnToggleChange() {
    this.currentPage = 1;
    this.pageSize = 10;
    this.notificationParams.itemsCount = 10;
    const getListWithDefaultParam = {
      ...this.notificationParams,
      page: 0,
      itemsCount: 10
    };
    this.getAllNotification(getListWithDefaultParam);
  }

  getAllNotification(params = this.notificationParams) {
    this.NotificationLoading = true;
    this.subscription.add(
      this.notificationService.getAllNotification(params).subscribe({
        next: res => {
          this.notificationsList = res.notifications.map(items => {
            return {
              ...items,
              isUnread: !items.isRead,
              isMoreDetails: false
            };
          });
          this.isAnyUnreadNotification();
          this.total = res.total;
          this.selectedUnreadNotifications = this.notificationsList.filter(item => item.isUnread);
          this.getUpdatedUnreadCount();
          this.NotificationLoading = false;
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  getUpdatedUnreadCount() {
    this.subscription.add(
      this.notificationService.getUpdatedNotificationCount(this.loggedUser.userId).subscribe({
        next: res => {
          this.unreadNotificationCount = res;
          this.notificationService.notificationCount$.next(res);
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  isAnyUnreadNotification() {
    this.isAnyCheckboxChecked = this.notificationsList.some(item => item.isUnread);
  }

  updateCheckboxStatus(userNotificationId: number, isRead: boolean) {
    this.isAnyUnreadNotification();
    const readUnreadParams = {
      userNotificationId: [userNotificationId],
      isRead: !isRead,
      isReadFromWeb: true
    };
    this.markSelectedAsReadUnRead(readUnreadParams);
  }

  markSelectedAsRead() {
    this.NotificationLoading = true;
    this.selectedUnreadNotifications = this.notificationsList.filter(item => item.isUnread);
    const notificationId = this.selectedUnreadNotifications.map(item => item.userNotificationId);
    const readUnreadParams = {
      userNotificationId: notificationId,
      isRead: true,
      isReadFromWeb: true
    };
    this.markSelectedAsReadUnRead(readUnreadParams, true);
  }

  markSelectedAsReadUnRead(readUnreadParams, isPageReset = false) {
    this.subscription.add(
      this.notificationService.markAsReadUnRead(readUnreadParams).subscribe({
        next: res => {
          this.NotificationLoading = false;
          this.selectedUnreadNotifications = [];
          if (isPageReset) {
            this.notificationParams.page = 0;
            this.currentPage = 1;
          }
          this.getAllNotification();
        },
        error: err => {
          this.NotificationLoading = false;
        }
      })
    );
  }

  navigateToEntity(notification: AllNotificationModal) {
    if (!notification.isDeleted) {
      const readUnreadParams = {
        userNotificationId: [notification.userNotificationId],
        isRead: true,
        isReadFromWeb: true
      };

      const navigateToWorkOrder = () => {
        const { assessmentType: assementType, frequencyType, entityId: id } = notification;
        this.router.navigate(['/entities/workorders/add'], {
          queryParams: { id, assementType, frequencyType }
        });
      };

      const navigateToTicketDetail = () => {
        this.router.navigateByUrl(`/entities/ticket/detail/view/${notification.ticketNumber}`);
      };

      const navigateToViewSiteCheckInCheckOut = () => {
        this.router.navigateByUrl(`/entities/safety/site-checkin/detail/view/${notification.entityId}`);
      };

      const navigateToAlert = () => {
        const url = `/entities/performance/outage?customerId=${notification.customerId}&siteId=${notification.siteId}&portfolioId=${notification.portfolioId}&date=${notification.notificationDate}`;
        this.router.navigateByUrl(url);
      };

      const markNotificationAsRead = () => {
        this.subscription.add(
          this.notificationService.markAsReadUnRead(readUnreadParams).subscribe({
            next: res => {
              this.NotificationLoading = false;
              this.selectedUnreadNotifications = [];
              this.getAllNotification();
            },
            error: err => {
              this.NotificationLoading = false;
            }
          })
        );
      };

      if (!notification.isRead) {
        markNotificationAsRead();
      }

      if (notification.triggerId === 1 || notification.triggerId === 15) {
        navigateToWorkOrder();
      } else if (notification.triggerId === 16 || notification.triggerId === 17) {
        navigateToViewSiteCheckInCheckOut();
      } else if (notification.triggerId === 18 || notification.triggerId === 20) {
        navigateToAlert();
      } else {
        navigateToTicketDetail();
      }
      this.closeNotificationDropdown();
    }
  }

  expandView() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-full-dialog notification-dialog'
    };
    this.modalRef = this.modalService.show(NotificationDetailsScreenComponent, ngModalOptions);
  }

  getInitials(name: string | undefined): string {
    if (name) {
      const nameArray: string[] = name.split(' ');
      const firstLetterOfName = nameArray[0]?.charAt(0);
      const secondLetterOfName = nameArray[nameArray.length - 1]?.charAt(0);
      if (nameArray.length > 1) {
        return `${firstLetterOfName || ''}${secondLetterOfName || ''}`.toUpperCase();
      } else {
        return (firstLetterOfName || '').toUpperCase();
      }
    }
    return '';
  }

  onChangeSize() {
    this.currentPage = 0;
    this.notificationParams.page = 0;
    this.notificationParams.itemsCount = Number(this.pageSize);
    this.getAllNotification();
  }

  onPageChange(obj) {
    this.currentPage = obj;
    this.notificationParams.page = this.currentPage - 1;
    this.getAllNotification();
  }

  closeNotificationDropdown() {
    const dropdownElement = this.myDropdown.nativeElement;
    if (dropdownElement) {
      dropdownElement.classList.remove('show');
    }
  }

  toggleMoreDetails(auditActionId: number, isMoreDetails) {
    this.NotificationLoading = true;
    const clickedNotification = this.notificationsList.find(item => item.auditActionId === auditActionId);
    if (isMoreDetails) {
      this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
      this.notificationActions = [];
      return;
    }
    if (clickedNotification && clickedNotification.auditActionId) {
      this.subscription.add(
        this.notificationService.getMoreDetailsOfNotification(clickedNotification).subscribe({
          next: res => {
            if (res && res.length) {
              const actionSummary = JSON.parse(res);
              for (const j of actionSummary) {
                j.Value = JSON.parse(j.Value);
              }
              this.notificationActions = actionSummary;
              this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
            } else {
              this.notificationActions = [];
              this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
            }
          },
          error: err => {
            this.NotificationLoading = false;
          }
        })
      );
    } else {
      this.notificationActions = [];
      this.toggleMoreDetailsIcon(auditActionId, clickedNotification);
    }
  }

  toggleMoreDetailsIcon(auditActionId, clickedNotification) {
    clickedNotification.isMoreDetails = !clickedNotification.isMoreDetails;
    this.notificationsList.forEach(item => {
      if (item.auditActionId !== auditActionId) {
        item.isMoreDetails = false;
      }
    });
    this.NotificationLoading = false;
  }

  getInitialsColorForBgColor(bgColor: string) {
    return this.notificationService.getInitialsColorForBgColor(bgColor);
  }

  showChunkuploadProgress() {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {},
      class: 'modal-lg'
    };
    this.modalRef = this.modalService.show(ChunkUploadProgressComponent, ngModalOptions);
  }

  getLocation(): void {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          this.qeAnalyticsGatheringService.captureQEAnalyticsItemEnum(QE_MENU_MODULE_ENUM.SITE_CHECK_IN, QE_MENU_MODULE_ENUM.OTHERS);
          const ngModalOptions: ModalOptions = {
            backdrop: 'static',
            keyboard: false,
            animated: true,
            class: 'modal-md',
            initialState: { position }
          };
          this.modalRef = this.modalService.show(SiteCheckinOutComponent, ngModalOptions);
        },
        error => {
          if (error.code === error.PERMISSION_DENIED) {
            console.error('User has blocked location access.');
          } else if (error.code === error.POSITION_UNAVAILABLE) {
            console.error('Location information is unavailable.');
          } else if (error.code === error.TIMEOUT) {
            console.error('The request to get the user location timed out.');
          } else {
            console.error('An unknown error occurred:', error.message);
          }
          // Notify the user about the denial and guide them to enable permissions
          alert('Location access problem. Please enable location .');
        },
        {
          timeout: 10000, // Timeout after 10 seconds
          enableHighAccuracy: true
        }
      );
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscription.unsubscribe();
  }
}
