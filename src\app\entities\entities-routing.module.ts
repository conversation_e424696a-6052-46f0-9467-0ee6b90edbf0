/**
 * @sunflowerlab
 * <AUTHOR>
 * Entities routing module. Define the lazy loading of all the modules here. Every module should be lazy loaded only.
 */
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DataNotFoundComponent } from '../@shared/components/data-not-found/data-not-found.component';
import { NotFoundComponent } from '../@shared/components/not-found/not-found.component';
import { PermissionGuard } from '../@shared/services/permission.guard';
import { EntitiesComponent } from './entities.component';
import { AddEditTemplateComponent } from './operations/custom-forms/add-edit-template/add-edit-template.component';
import { QEAnalyticsGatheringGuard } from './qe-analytics/services/qe-analytics-gathering.guard';
import { QE_MENU_MODULE_ENUM } from '../@shared/enums/qe-menu.enum';

const routes: Routes = [
  {
    path: '',
    component: EntitiesComponent,
    children: [
      {
        path: 'dashboard',
        loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule),
        canActivate: [QEAnalyticsGatheringGuard],
        data: { qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PM_DASHBOARD, qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE }
      },
      {
        path: 'admin/users',
        loadChildren: () => import('./user-management/user-management.module').then(m => m.UserManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'commercialAssetsManager'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_USERS,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'operations',
        loadChildren: () => import('./operations/operations.module').then(m => m.OperationsModule),
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'commercialAssetsManager', 'fieldTech', 'portfolioManager', 'analyst']
        }
      },
      {
        path: 'admin/customer-api-gateway',
        loadChildren: () => import('./customer-api-gateway/customer-api-gateway.module').then(m => m.CustomerAPIGatewayModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'admin/api-gateway-dashboard',
        loadChildren: () => import('./api-gateway-dashboard/api-gateway-dashboard.module').then(m => m.APIGatewayDashboardModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'admin/dataSource',
        loadChildren: () => import('./data-source-management/data-source-management.module').then(m => m.DataSourceManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'admin/api-error-log',
        loadChildren: () => import('./error-log/error-log.module').then(m => m.ErrorLogModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'customers',
        loadChildren: () => import('./customer-management/customer-management.module').then(m => m.CustomerManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst', 'customer'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SI_CUSTOMERS,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SITE_INFO
        }
      },
      {
        path: 'portfolios',
        loadChildren: () => import('./portfolio-management/portfolio-management.module').then(m => m.PortfolioManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst', 'customer'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SI_PORTFOLIOS,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SITE_INFO
        }
      },
      {
        path: 'sites',
        loadChildren: () => import('./site-management/site-management.module').then(m => m.SiteManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst', 'customer'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SI_SITES,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SITE_INFO
        }
      },
      {
        path: 'assessments',
        loadChildren: () => import('./assessment-management/assessment-management.module').then(m => m.AssessmentManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PM_SCOPE,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE
        }
      },
      {
        path: 'workorders',
        loadChildren: () => import('./workorder-management/workorder-management.module').then(m => m.WorkorderManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE
        }
      },
      {
        path: 'reports',
        loadChildren: () => import('./report/report.module').then(m => m.ReportModule),
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst', 'customer']
        },
        children: [
          {
            path: 'sitevisits',
            loadChildren: () => import('./report/sitevisit-report/sitevisit-report.module').then(m => m.SitevisitReportModule)
          },
          {
            path: 'jhas',
            loadChildren: () => import('./report/jha-report/jha-report.module').then(m => m.JhaReportModule)
          },
          {
            path: 'vegetation',
            loadChildren: () => import('./report/vegetation-report/vegetation-report.module').then(m => m.VegetationReportModule)
          },
          {
            path: 'mvpm',
            loadChildren: () => import('./report/mvpm-report/mvpm-report.module').then(m => m.MvpmReportModule)
          }
        ]
      },
      {
        path: 'other-reports',
        loadChildren: () => import('./reports/reports.module').then(m => m.ReportsModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst', 'customer'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PM_REPORTS,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE
        }
      },
      {
        path: 'site-audit-report',
        loadChildren: () => import('./site-audit-report/site-audit-report.module').then(m => m.SiteAuditReportModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PM_SITE_AUDIT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE
        }
      },
      {
        path: 'non-conformance',
        loadChildren: () => import('./pm-non-conformance/pm-non-conformance.module').then(m => m.PmNonConformanceModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'commercialAssetsManager'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE
        }
      },
      {
        path: 'modal-overlays',
        loadChildren: () => import('./modal-overlays/modal-overlays.module').then(m => m.ModalOverlaysModule)
      },
      {
        path: 'profile',
        loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule),
        canActivate: [PermissionGuard]
      },
      {
        path: 'logs',
        loadChildren: () => import('./logs/logs.module').then(m => m.LogsModule)
      },
      {
        path: 'site-device',
        loadChildren: () => import('./site-device/site-device.module').then(m => m.SiteDeviceModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst', 'customer'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SI_DEVICES,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SITE_INFO
        }
      },
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'equipment',
        loadChildren: () => import('./equipment-management/equipment-management.module').then(m => m.EquipmentManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SI_EQUIPMENT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SITE_INFO
        }
      },
      {
        path: 'ticket',
        loadChildren: () => import('./ticket-management/ticket-management.module').then(m => m.TicketManagementModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'customer', 'analyst'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'cm-reports',
        loadChildren: () => import('./cm-reports/cm-reports.module').then(m => m.CmReportsModule),
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'fieldTech', 'customer']
        }
      },
      {
        path: 'cm-dashboard',
        loadChildren: () => import('./cm-dashboard/cm-dashboard.module').then(m => m.CmDashboardModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'fieldTech'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_DASHBOARD,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'site-dashboard',
        loadChildren: () => import('./site-dashboard/site-dashboard.module').then(m => m.SiteDashboardModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'fieldTech'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SI_DASHBOARD,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SITE_INFO
        }
      },
      {
        path: 'performance',
        loadChildren: () => import('./performance/performance.module').then(m => m.PerformanceModule),
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'fieldTech', 'customer']
        }
      },
      {
        path: 'safety',
        loadChildren: () => import('./safety/safety.module').then(m => m.SafetyModule),
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'fieldTech', 'customer']
        }
      },
      {
        path: 'availability',
        loadChildren: () => import('./availability/availability.module').then(m => m.AvailabilityModule),
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'fieldTech']
        }
      },
      {
        path: 'admin/report-schedule',
        loadChildren: () => import('./report-scheduler/report-scheduler.module').then(m => m.ReportSchedulerModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'admin/refetch-schedule',
        loadChildren: () => import('./refatch-scheduler/refatch-scheduler.module').then(m => m.RefatchSchedulerModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'admin/email-log',
        loadChildren: () => import('./email-log/email-log.module').then(m => m.EmailLogModule),
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin'],
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AD_EMAIL_LOG,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.ADMIN
        }
      },
      {
        path: 'admin/analytics',
        loadChildren: () => import('./qe-analytics/qe-analytics.module').then(m => m.QEAnalyticsModule),
        canActivate: [PermissionGuard],
        data: { permittedRoles: ['admin'] }
      },
      {
        path: 'fill-mobile-form/:id',
        component: AddEditTemplateComponent
      },
      {
        path: 'data-not-found',
        component: DataNotFoundComponent
      },
      {
        path: '**',
        component: NotFoundComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EntitiesRoutingModule {}
