<nb-card class="qe-analytics-spinner appSpinner" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <div class="row">
      <div class="col-12 d-flex align-items-center">
        <h6>Analytics</h6>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="row">
      <div class="col-12 qe-analytics-filter appFilter mb-3">
        <sfl-filter
          [filterDetails]="filterDetails"
          [isApiErrorFilter]="isApiErrorFilter"
          (refreshList)="refreshList($event)"
          (refreshTableHeight)="this.isFilterDisplay = $event"
        ></sfl-filter>
      </div>
      <div id="fixed-table" setTableHeight [isFilterDisplay]="isFilterDisplay" class="col-12 table-responsive table-card-view">
        <table class="table table-hover table-bordered" aria-describedby="Analytics">
          <thead>
            <tr>
              <th *ngFor="let category of analyticsCategories">{{ category.name }}</th>
            </tr>
          </thead>
            <tbody>
            <tr *ngFor="let row of maxItemsArray">
              <ng-container *ngFor="let category of analyticsCategories"></ng-container>
                <ng-container *ngIf="category.items[row]">
                  <div>
                    <span>{{ category.items[row].label }}</span>
                    <span class="float-end">{{ category.items[row].percent }}</span>
                  </div>
                  <!-- Render childrens if present -->
                  <div *ngIf="category.items[row].childrens">
                    <div *ngFor="let child of category.items[row].childrens" class="ms-3">
                      <span>{{ child.label }}</span>
                      <span class="float-end">{{ child.percent }}</span>
                    </div>
                  </div>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </nb-card-body>
</nb-card>
