import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { QEAnalyticsPasswordObj, QEMenuModuleType } from '../models/qe-analytics.model';
import { delay, from, Observable, of } from 'rxjs';
import { ApiUrl, AppConstants } from '../../../@shared/constants';
import { QE_MENU_MODULE_NAME_ENUM, QE_MENU_MODULE_NAME_ENUM_LIST } from '../../../@shared/enums/qe-menu.enum';
import { StorageService } from '../../../@shared/services/storage.service';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsService {
  qeMenuModuleList: QEMenuModuleType[] = [];
  constructor(private readonly http: HttpClient, private readonly storageService: StorageService) {}

  private mapEnumIdsToMenuItem(item: QEMenuModuleType): QEMenuModuleType {
    const menuNameEnumMap = Object.fromEntries(Object.entries(QE_MENU_MODULE_NAME_ENUM).map(([k, v]) => [v, +k]));
    const menuNameEnumListMap = (obj: any) => Object.fromEntries(Object.entries(obj).map(([k, v]) => [v, +k]));

    return {
      ...item,
      menuParentId: menuNameEnumMap[item.parentName] ?? item.menuParentId,
      menuUniqueId: menuNameEnumListMap(QE_MENU_MODULE_NAME_ENUM_LIST[menuNameEnumMap[item.parentName]])[item.menuName] ?? item.menuUniqueId
    };
  }

  setQEMenuModuleListWithEnumMapping(): QEMenuModuleType[] {
    const storedQEMenuModuleTypeList = this.storageService.get(AppConstants.qeMenuSubmenuItemListKey);
    const qeMenuModuleTypeList =
      storedQEMenuModuleTypeList && storedQEMenuModuleTypeList.length > 0 ? storedQEMenuModuleTypeList : this.qeMenuModuleList;
    return qeMenuModuleTypeList.map(this.mapEnumIdsToMenuItem);
  }

  checkAndUpdateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): Observable<QEAnalyticsPasswordObj> {
    return this.http.post<QEAnalyticsPasswordObj>(ApiUrl.CHECK_UPDATE_QE_ANALYTICS_PASSWORD, qeAnalyticsPasswordObj);
  }

  resetQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): Observable<QEAnalyticsPasswordObj> {
    return this.http.post<QEAnalyticsPasswordObj>(ApiUrl.RESET_QE_ANALYTICS_PASSWORD, qeAnalyticsPasswordObj);
  }

  getQEMenuModuleList(): Observable<QEMenuModuleType[]> {
    const qeMenuModuleList = [
      {
        menuId: 1,
        menuName: 'Dashboard',
        parentName: 'Site Info',
        parentId: 1,
        menuLevelOrder: 1
      },
      {
        menuId: 2,
        menuName: 'Customers',
        parentName: 'Site Info',
        parentId: 1,
        menuLevelOrder: 1
      },
      {
        menuId: 3,
        menuName: 'Portfolios',
        parentName: 'Site Info',
        parentId: 1,
        menuLevelOrder: 1
      },
      {
        menuId: 4,
        menuName: 'Sites',
        parentName: 'Site Info',
        parentId: 1,
        menuLevelOrder: 1
      },
      {
        menuId: 5,
        menuName: 'Devices',
        parentName: 'Site Info',
        parentId: 1,
        menuLevelOrder: 1
      },
      {
        menuId: 6,
        menuName: 'Equipment',
        parentName: 'Site Info',
        parentId: 1,
        menuLevelOrder: 1
      },
      {
        menuId: 7,
        menuName: 'Dashboard',
        parentName: 'PM',
        parentId: 2,
        menuLevelOrder: 1
      },
      {
        menuId: 8,
        menuName: 'Scope',
        parentName: 'PM',
        parentId: 2,
        menuLevelOrder: 1
      },
      {
        menuId: 9,
        menuName: 'Work Orders',
        parentName: 'PM',
        parentId: 2,
        menuLevelOrder: 1
      },
      {
        menuId: 10,
        menuName: 'Reports',
        parentName: 'PM',
        parentId: 2,
        menuLevelOrder: 1
      },
      {
        menuId: 11,
        menuName: 'Site Audit',
        parentName: 'PM',
        parentId: 2,
        menuLevelOrder: 1
      },
      {
        menuId: 12,
        menuName: 'Non-Conformance',
        parentName: 'PM',
        parentId: 2,
        menuLevelOrder: 1
      },
      {
        menuId: 13,
        menuName: 'Dashboard',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 14,
        menuName: 'All Tickets',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 15,
        menuName: 'Ticket Audit Report',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 16,
        menuName: 'Exclusion Report',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 17,
        menuName: 'Billing Report',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 18,
        menuName: 'Truck Roll Report',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 19,
        menuName: 'Map Report',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 20,
        menuName: 'RMA Report',
        parentName: 'CM',
        parentId: 3,
        menuLevelOrder: 1
      },
      {
        menuId: 21,
        menuName: 'Dashboard',
        parentName: 'Availability',
        parentId: 4,
        menuLevelOrder: 1
      },
      {
        menuId: 22,
        menuName: 'Reports',
        parentName: 'Availability',
        parentId: 4,
        menuLevelOrder: 1
      },
      {
        menuId: 23,
        menuName: 'Data Table',
        parentName: 'Availability',
        parentId: 4,
        menuLevelOrder: 1
      },
      {
        menuId: 24,
        menuName: 'Exclusions',
        parentName: 'Availability',
        parentId: 4,
        menuLevelOrder: 1
      },
      {
        menuId: 25,
        menuName: 'Dashboard',
        parentName: 'Performance',
        parentId: 5,
        menuLevelOrder: 1
      },
      {
        menuId: 26,
        menuName: 'Power Charts',
        parentName: 'Performance',
        parentId: 5,
        menuLevelOrder: 1
      },
      {
        menuId: 27,
        menuName: 'Reports',
        parentName: 'Performance',
        parentId: 5,
        menuLevelOrder: 1
      },
      {
        menuId: 28,
        menuName: 'Data Table',
        parentName: 'Performance',
        parentId: 5,
        menuLevelOrder: 1
      },
      {
        menuId: 29,
        menuName: 'Alerts',
        parentName: 'Performance',
        parentId: 5,
        menuLevelOrder: 1
      },
      {
        menuId: 30,
        menuName: 'JHA',
        parentName: 'Safety',
        parentId: 6,
        menuLevelOrder: 1
      },
      {
        menuId: 31,
        menuName: 'Site Check-In',
        parentName: 'Safety',
        parentId: 6,
        menuLevelOrder: 1
      },
      {
        menuId: 32,
        menuName: 'Site Audit-JHA',
        parentName: 'Safety',
        parentId: 6,
        menuLevelOrder: 1
      },
      {
        menuId: 33,
        menuName: 'General Info',
        parentName: 'Settings',
        parentId: 7,
        menuLevelOrder: 2
      },
      {
        menuId: 34,
        menuName: 'Work Type',
        parentName: 'Settings',
        parentId: 7,
        menuLevelOrder: 2
      },
      {
        menuId: 35,
        menuName: 'Work Step',
        parentName: 'Settings',
        parentId: 7,
        menuLevelOrder: 2
      },
      {
        menuId: 36,
        menuName: 'Hazard',
        parentName: 'Settings',
        parentId: 7,
        menuLevelOrder: 2
      },
      {
        menuId: 37,
        menuName: 'Barrier',
        parentName: 'Settings',
        parentId: 7,
        menuLevelOrder: 2
      },

      {
        menuId: 38,
        menuName: 'Reports',
        parentName: 'Operations',
        parentId: 8,
        menuLevelOrder: 1
      },
      {
        menuId: 39,
        menuName: 'Region Mapping',
        parentName: 'Operations',
        parentId: 8,
        menuLevelOrder: 1
      },
      {
        menuId: 40,
        menuName: 'Services',
        parentName: 'Operations',
        parentId: 8,
        menuLevelOrder: 1
      },
      {
        menuId: 41,
        menuName: 'Contracts',
        parentName: 'Operations',
        parentId: 8,
        menuLevelOrder: 1
      },
      {
        menuId: 42,
        menuName: 'Custom Forms',
        parentName: 'Operations',
        parentId: 8,
        menuLevelOrder: 1
      },
      {
        menuId: 43,
        menuName: 'Users',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 44,
        menuName: 'Data Source Mapping',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 45,
        menuName: 'API Error Log',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 46,
        menuName: 'Report Scheduler',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 47,
        menuName: 'Customer API Gateway',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 48,
        menuName: 'API Gateway Dashboard',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 49,
        menuName: 'Re-Fetch Scheduler',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 50,
        menuName: 'Email Log',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 51,
        menuName: 'Analytics',
        parentName: 'Admin',
        parentId: 9,
        menuLevelOrder: 1
      },
      {
        menuId: 52,
        menuName: 'Site Check-In',
        parentName: 'Others',
        parentId: 10,
        menuLevelOrder: 1
      },
      {
        menuId: 53,
        menuName: 'User Profile',
        parentName: 'Others',
        parentId: 10,
        menuLevelOrder: 1
      },
      {
        menuId: 54,
        menuName: 'Change Password',
        parentName: 'Others',
        parentId: 10,
        menuLevelOrder: 1
      }
    ] as QEMenuModuleType[];

    const qeMenuModuleTypeListResponse = new Promise<QEMenuModuleType[]>((resolve, reject) => {
      if (this.qeMenuModuleList.length > 0) {
        this.storageService.set(AppConstants.qeMenuSubmenuItemListKey, this.qeMenuModuleList);
        resolve(this.qeMenuModuleList);
      } else {
        of(qeMenuModuleList).subscribe({
          next: (res: QEMenuModuleType[]) => {
            this.qeMenuModuleList = res.map(this.mapEnumIdsToMenuItem);
            this.storageService.set(AppConstants.qeMenuSubmenuItemListKey, this.qeMenuModuleList);
            resolve(this.qeMenuModuleList);
          },
          error: e => {
            reject(e);
          }
        });
        // this.http.get<QEMenuModuleType[]>(ApiUrl.GET_QE_MENU_MODULE_LIST).subscribe({
        //   next: (res: QEMenuModuleType[]) => {
        //     this.qeMenuModuleList = res.map(this.mapEnumIdsToMenuItem);
        //     this.storageService.set(AppConstants.qeMenuSubmenuItemListKey, this.qeMenuModuleList);
        //     resolve(this.qeMenuModuleList);
        //   },
        //   error: e => {
        //     reject(e);
        //   }
        // });
      }
    });
    return from(qeMenuModuleTypeListResponse);
  }

  trackQEMenuItem(trackQEMenuReq): Observable<any> {
    return this.http.post<any>(ApiUrl.GET_QE_MENU_MODULE_LIST, trackQEMenuReq);
  }
}
