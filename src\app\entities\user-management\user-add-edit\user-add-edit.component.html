<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header>
    <h6>{{ getComponentTitle() }}</h6>
  </nb-card-header>
  <nb-card-body class="dropdownOverflow">
    <form name="form" #form="ngForm" aria-labelledby="title" autocomplete="off">
      <div class="form-group row">
        <div class="col-md-6 mb-2">
          <div class="form-control-group">
            <label *ngIf="!showSite" class="label" for="input-name"> First Name<span class="ms-1 text-danger">*</span></label>
            <label *ngIf="showSite" class="label" for="input-name"> First Name / Group Name<span class="ms-1 text-danger">*</span></label>

            <input
              nbInput
              fullWidth
              [(ngModel)]="user.firstName"
              #firstName="ngModel"
              name="firstName"
              id="input-name"
              pattern=".*\S.*"
              class="form-control"
              spellcheck="true"
              contenteditable="true"
              [status]="firstName.dirty ? (firstName.invalid ? 'danger' : 'success') : 'basic'"
              required
              [attr.aria-invalid]="firstName.invalid && firstName.touched ? true : null"
            />
            <ng-container *ngIf="firstName.invalid && firstName.touched">
              <p class="caption status-danger" *ngIf="firstName.errors?.required">First name is required</p>
              <p class="caption status-danger" *ngIf="firstName.errors?.pattern">Only space not allow in first name field.</p>
            </ng-container>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-control-group">
            <label class="label" for="input-name"> Last Name<span class="ms-1 text-danger">*</span></label>
            <input
              nbInput
              fullWidth
              [(ngModel)]="user.lastName"
              #lastNames="ngModel"
              name="lastName"
              id="input-name"
              pattern=".*\S.*"
              class="form-control"
              spellcheck="true"
              contenteditable="true"
              required
            />
            <sfl-error-msg [control]="lastNames" [isFormSubmitted]="form?.submitted" fieldName="lastName"></sfl-error-msg>
          </div>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-md-6 mb-2">
          <div class="form-control-group">
            <label class="label" for="input-email">Email<span class="ms-1 text-danger">*</span></label>
            <input
              nbInput
              fullWidth
              [(ngModel)]="user.email"
              #email="ngModel"
              name="email"
              id="input-email"
              class="form-control"
              [disabled]="isEdit"
              [status]="email.dirty ? (email.invalid ? 'danger' : 'success') : 'basic'"
              required
              pattern=".+@.+\..+"
              [attr.aria-invalid]="email.invalid && email.touched ? true : null"
            />
            <ng-container *ngIf="email.invalid && email.touched">
              <p class="caption status-danger" *ngIf="email.errors?.required">Email is required</p>
              <p class="caption status-danger" *ngIf="email.errors?.pattern">Email format is incorrect</p>
            </ng-container>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-control-group">
            <label class="label" for="input-phone">Phone</label>
            <input
              nbInput
              fullWidth
              [(ngModel)]="user.phoneNo"
              #phone="ngModel"
              [mask]="contactNoFormat"
              name="phone"
              id="input-phone"
              class="form-control"
            />
          </div>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-md-6 mb-2">
          <label class="label" for="input-Role">Role<span class="ms-1 text-danger">*</span></label>
          <ng-select
            fullWidth
            id="Role"
            name="Role"
            required
            [(ngModel)]="user.Role"
            #Role="ngModel"
            size="large"
            (change)="onChange($event)"
            [clearable]="false"
            appendTo="body"
          >
            <ng-option *ngFor="let role of roles" [value]="role.name">
              {{ role.displayName }}
            </ng-option>
          </ng-select>
          <div *ngIf="Role.dirty && Role.invalid">
            <small class="text-danger"> Please enter Role </small>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <label class="label" for="input-State">State</label>
          <ng-select
            fullWidth
            id="State"
            name="State"
            #State="ngModel"
            [(ngModel)]="user.state"
            placeholder="Select"
            size="large"
            [clearable]="false"
            appendTo="body"
          >
            <ng-option *ngFor="let state of states" [value]="state.name">
              {{ state?.name }}
            </ng-option>
          </ng-select>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-md-6 mb-2">
          <div class="form-control-group">
            <label class="label" for="input-zipCode">Zip Code</label>
            <input
              nbInput
              fullWidth
              [(ngModel)]="user.zipCode"
              #zipCode="ngModel"
              sflNumbersOnly
              name="zipCode"
              id="input-zipCode"
              class="form-control"
              [maxlength]="6"
            />
          </div>
        </div>

        <div class="col-md-6 mb-2">
          <label for="inputEmail" class="label">Time Zone</label>
          <ng-select
            name="timeZone"
            [items]="timeZoneList"
            bindLabel="displayName"
            bindValue="id"
            #timeZone="ngModel"
            [(ngModel)]="user.userTimeZone"
            notFoundText="No TimeZone Found"
            placeholder="Select TimeZone"
            appendTo="body"
            [clearable]="false"
          >
          </ng-select>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-md-6 mb-2">
          <label class="label" for="input-portfolioAccess"> Portfolio Access</label>
          <nb-toggle
            *ngIf="showPortfolio"
            status="primary"
            [(checked)]="user.portfolioAccess.length && portfolioData.length === user.portfolioAccess.length"
            (checkedChange)="changePortfolioAccess($event)"
            labelPosition="start"
          >
            <span class="ms-1 text-danger">*</span></nb-toggle
          >
          <span class="ms-3"></span>
          <label *ngIf="showPortfolio" class="label" for="input-portfolioAccess">All Portfolios</label>
          <ng-select
            name="Portfolio"
            [multiple]="true"
            [items]="portfolioData"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="user.portfolioAccess"
            notFoundText="No portfolio access Found"
            placeholder="Select Portfolio Access"
            [closeOnSelect]="false"
            (change)="onPortfolioChange()"
            appendTo="body"
            (search)="onFilter($event, 'filteredPortfolioIds')"
            (close)="filteredPortfolioIds = []"
            (remove)="onPortfolioRemove($event.value)"
            (clear)="onPortfolioClear($event)"
            (add)="onPortfolioAdd($event)"
            #portfolioAccess="ngModel"
            [required]="showPortfolio"
          >
            <ng-template ng-header-tmp>
              <button type="button" (click)="selectUnselectAllPortfolio(false, true)" class="btn btn-sm btn-primary me-2">
                Select all
              </button>
              <button type="button" (click)="selectUnselectAllPortfolio(false, false)" class="btn btn-sm btn-primary ml-2">
                Unselect all
              </button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
          <sfl-error-msg [control]="portfolioAccess" [isFormSubmitted]="form?.submitted" fieldName="Portfolio Access"></sfl-error-msg>
        </div>

        <div class="col-md-6 mb-2" *ngIf="showSite">
          <label class="label me-1" for="input-site">Site</label>
          <nb-toggle
            status="primary"
            [(checked)]="user.siteAccess.length && siteList.length === user.siteAccess.length"
            (checkedChange)="changSiteAccess($event)"
            labelPosition="start"
            [disabled]="!user.portfolioAccess.length"
          >
          </nb-toggle>
          <span class="ms-3"></span>
          <label class="label" for="input-site">All Sites</label>
          <ng-select
            name="Site"
            [multiple]="true"
            [items]="siteList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="user.siteAccess"
            notFoundText="No site Found"
            placeholder="Select Site"
            [closeOnSelect]="false"
            appendTo="body"
            (search)="onFilter($event, 'filteredSiteIds')"
            (close)="filteredSiteIds = []"
            [virtualScroll]="true"
          >
            <ng-template ng-header-tmp>
              <button type="button" (click)="selectUnselectAllSite(false, true)" class="btn btn-sm btn-primary me-2">Select all</button>
              <button type="button" (click)="selectUnselectAllSite(false, false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="form-group row" *ngIf="isPortfolioAssignmentRole() && isEdit">
        <div class="col-md-6 mb-2">
          <label class="label" for="input-portfolioAssignment"> {{ userLabel }} Assignment </label>
          <ng-select
            name="assignment"
            [multiple]="true"
            [items]="portfolioAssignmentData"
            bindLabel="name"
            bindValue="id"
            #assignment="ngModel"
            [(ngModel)]="user.assignment"
            [notFoundText]="'No ' + userLabel + ' Assignment Found'"
            [placeholder]="'Select ' + userLabel + ' Assignment'"
            [closeOnSelect]="false"
            appendTo="body"
            (search)="onFilter($event, 'filteredPortfolioAssignmentIds')"
            (close)="filteredPortfolioAssignmentIds = []; setPortfolioAssignmentDropdown()"
            (open)="setPortfolioAssignmentDropdown()"
          >
            <ng-template ng-header-tmp>
              <button type="button" (click)="selectUnselectAllPortfolioAssignment(false, true)" class="btn btn-sm btn-primary me-2">
                Select all
              </button>
              <button type="button" (click)="selectUnselectAllPortfolioAssignment(false, false)" class="btn btn-sm btn-primary ml-2">
                Unselect all
              </button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" /> {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
          <sfl-error-msg
            [control]="assignment"
            [isFormSubmitted]="form?.submitted"
            fieldName="{{ userLabel + ' Assignment' }}"
          ></sfl-error-msg>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-sm-6 my-2">
          <nb-toggle status="primary" [(checked)]="user.isActive" labelPosition="start"
            ><label class="label m-0">Active</label> <span class="ms-1 text-danger">*</span></nb-toggle
          >
        </div>
        <div class="col-sm-6 my-2">
          <nb-toggle status="primary" [disabled]="loggedUser[0] !== 'admin'" [(checked)]="user.allowNotification" labelPosition="start"
            ><label class="label m-0">Allow Notification</label></nb-toggle
          >
        </div>
      </div>
      <div class="form-group row">
        <div class="col-md-6 my-2">
          <nb-toggle
            status="primary"
            [(checked)]="user.twoFactorEnabled"
            [disabled]="loggedUser[0] !== 'admin'"
            (checkedChange)="isEdit && currentValueForTwoFactorEnabled !== null ? (user.reason = user.MFAPassword = '') : null"
            labelPosition="start"
            ><label class="label m-0">Enable MFA</label>
          </nb-toggle>
        </div>
        <div class="col-md-6 my-2">
          <ng-container *ngIf="isEdit">
            <div class="row">
              <ng-container *ngIf="currentValueForTwoFactorEnabled !== null && user.twoFactorEnabled !== currentValueForTwoFactorEnabled">
                <div class="col-md-6">
                  <label class="label" for="input-name"> Reason<span class="ms-1 text-danger">*</span></label>
                  <input
                    nbInput
                    fullWidth
                    [(ngModel)]="user.reason"
                    #reason="ngModel"
                    name="reason"
                    id="reason"
                    pattern=".*\S.*"
                    class="form-control"
                    spellcheck="true"
                    contenteditable="true"
                    placeholder="Add Reason"
                    [required]="currentValueForTwoFactorEnabled !== null && user.twoFactorEnabled !== currentValueForTwoFactorEnabled"
                  />
                  <sfl-error-msg [control]="reason" [isFormSubmitted]="form?.submitted" fieldName="Reason"></sfl-error-msg>
                </div>
                <div class="col-md-6">
                  <label class="label" for="input-mfapassword"> Password<span class="ms-1 text-danger">*</span></label>
                  <input
                    nbInput
                    fullWidth
                    [(ngModel)]="user.MFAPassword"
                    #MFAPassword="ngModel"
                    name="MFAPassword"
                    id="MFAPassword"
                    type="password"
                    autocomplete="off"
                    pattern=".*\S.*"
                    class="form-control"
                    spellcheck="true"
                    contenteditable="true"
                    placeholder="Password"
                    [required]="currentValueForTwoFactorEnabled !== null && user.twoFactorEnabled !== currentValueForTwoFactorEnabled"
                  />
                  <sfl-error-msg [control]="MFAPassword" [isFormSubmitted]="form?.submitted" fieldName="Password"></sfl-error-msg>
                </div>
              </ng-container>
            </div>
          </ng-container>
        </div>
      </div>

      <div class="form-group d-flex justify-content-end">
        <button
          nbButton
          status="primary"
          size="medium"
          type="submit"
          id="userSubmit"
          (click)="createUser()"
          [disabled]="!form.valid"
          class="float-end m-1"
        >
          Save
        </button>
        <button nbButton status="basic" type="button" routerLink="/entities/admin/users" size="medium" class="float-end m-1">cancel</button>
      </div>
    </form>

    <ng-container *ngIf="isEdit">
      <nb-accordion class="mt-4">
        <nb-accordion-item class="border-bottom">
          <nb-accordion-item-header class="accordion_head">
            <strong>Audit Logs</strong>
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <div class="row">
              <div class="col-12">
                <ng-container *ngFor="let userAuthenticationAuditLogItem of userAuthenticationAuditLogs; let i = index">
                  <nb-accordion class="mb-3">
                    <nb-accordion-item class="border-bottom">
                      <nb-accordion-item-header class="accordion_head">
                        <strong>{{ userAuthenticationAuditLogItem?.userName }}</strong>
                        <div class="label mb-0 ms-2">{{ userAuthenticationAuditLogItem?.action }}</div>
                        <span class="label mb-0 ms-2"> - </span>
                        <span class="label mb-0 ms-2">{{ userAuthenticationAuditLogItem?.logDate }}</span>
                      </nb-accordion-item-header>
                      <nb-accordion-item-body>
                        <div class="row">
                          <div class="col-lg-8 col-xl-6 col-12">
                            <div id="" class="mt-2">
                              <table class="table table-hover table-bordered" aria-describedby="Audit Logs List">
                                <tbody>
                                  <ng-container
                                    *ngFor="let auditLogDetailItem of userAuthenticationAuditLogItem.auditLogDetails; let i = index"
                                  >
                                    <tr>
                                      <td *ngIf="auditLogDetailItem.fieldName" class="col-6">{{ auditLogDetailItem.fieldName }}</td>
                                      <td *ngIf="auditLogDetailItem.newValue" class="col-6">{{ auditLogDetailItem.newValue }}</td>
                                    </tr>
                                  </ng-container>
                                  <tr>
                                    <td
                                      colspan="2"
                                      *ngIf="!userAuthenticationAuditLogItem?.auditLogDetails?.length"
                                      class="no-record text-center"
                                    >
                                      No logs found
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      </nb-accordion-item-body>
                    </nb-accordion-item>
                  </nb-accordion>
                </ng-container>
              </div>
            </div>
            <div class="row col-12 text-center" *ngIf="!userAuthenticationAuditLogs.length">
              <label>No logs found</label>
            </div>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </ng-container>
  </nb-card-body>
</nb-card>
