.img-content {
  text-align: center;
  overflow: auto;
  padding: 10px;
  border: 1px solid lightgray;
}

.img-display {
  visibility: hidden;
}

.canvas-display {
  border: 1px solid black;
  cursor: crosshair !important;
  background-repeat: no-repeat;
  background-size: 100% auto;
}

.canvas-display_generate_siteMap {
  display: none;
}

.canvas-save {
  border: 1px solid black;
  display: none;
}

.reportSiteImage {
  width: 80px;
  height: 50px;
  cursor: pointer !important;
}

.large-modal {
  max-width: 100% !important;
}

::ng-deep .modal-content nb-spinner {
  align-items: flex-start !important;
  padding: 8% !important;
}

.cdk-global-overlay-wrapper,
.cdk-overlay-container {
  z-index: 99999 !important;
}

.canvas-display-viewdeleted {
  border: 1px solid black;
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.disablebtn {
  background-color: #ccc !important;
  border-color: #ccc !important;
}
.delete-icon {
  text-align: right;
  margin-right: 15px;
  margin-bottom: 10px;
}
