<button hidden type="button" #qeAnalyticsPasswordButton (click)="openModal(qeAnalyticsPasswordTemplate, pwdOperation.PASSWORD)"></button>
<button
  hidden
  type="button"
  #qeAnalyticsChangePasswordButton
  (click)="openModal(qeAnalyticsChangePasswordTemplate, pwdOperation.CHANGE_PASSWORD)"
></button>
<button hidden type="button" #qeAnalyticsResetPasswordButton (click)="resetQEAnalyticsPassword(pwdOperation.FORGOT_PASSWORD)"></button>

<ng-template #qeAnalyticsPasswordTemplate>
  <div [nbSpinner]="loading" nbSpinnerStatus="primary">
    <div class="modal-header">
      <h4 class="modal-title pull-left">Enter Password</h4>

      <button type="button" class="close" aria-label="Close" (click)="onPasswordVerified(true)">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
    <form
      name="qeAnalyticsPasswordForm"
      #qeAnalyticsPasswordForm="ngForm"
      aria-labelledby="title"
      (ngSubmit)="qeAnalyticsPasswordForm.valid && onSubmit(qeAnalyticsPasswordForm)"
      autocomplete="off"
    >
      <div class="modal-body">
        <div class="form-group row">
          <label for="input-currentPassword" class="label mb-0 col-form-label">Password<span class="ms-1 text-danger">*</span></label>
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              [(ngModel)]="qeAnalyticsPasswordObj.currentPassword"
              #currentPassword="ngModel"
              name="currentPassword"
              type="password"
              id="input-currentPassword"
              class="form-control"
              required
            />
            <sfl-error-msg
              [control]="currentPassword"
              [isFormSubmitted]="qeAnalyticsPasswordForm?.submitted"
              fieldName="Password"
            ></sfl-error-msg>
          </div>
        </div>
        <div class="form-group row">
          <div class="d-flex justify-content-between">
            <span class="label-with-link mt-1 cursor-pointer">
              <a class="forgot-password caption-2 text-link" (click)="qeAnalyticsResetPasswordButton.click()">Reset Password?</a>
            </span>
            <span class="label-with-link mt-1 cursor-pointer">
              <a class="change-password caption-2 text-link" (click)="qeAnalyticsChangePasswordButton.click()">Change Password?</a>
            </span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" [disabled]="qeAnalyticsPasswordForm.invalid">Continue</button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #qeAnalyticsChangePasswordTemplate>
  <div [nbSpinner]="loading" nbSpinnerStatus="primary">
    <div class="modal-header">
      <h4 class="modal-title pull-left">Change Password</h4>

      <button type="button" class="close" aria-label="Close" (click)="onPasswordVerified(false)">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
    <form
      name="qeAnalyticsChangePassForm"
      #qeAnalyticsChangePassForm="ngForm"
      aria-labelledby="title"
      (ngSubmit)="qeAnalyticsChangePassForm.valid && onSubmit(qeAnalyticsChangePassForm)"
      autocomplete="off"
    >
      <div class="modal-body">
        <div class="form-group row">
          <label for="input-currentPassword" class="label mb-0 col-form-label"
            >Current Password<span class="ms-1 text-danger">*</span></label
          >
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              [(ngModel)]="qeAnalyticsPasswordObj.currentPassword"
              #currentPassword="ngModel"
              name="currentPassword"
              type="password"
              id="input-currentPassword"
              class="form-control"
              required
            />
            <sfl-error-msg
              [control]="currentPassword"
              [isFormSubmitted]="qeAnalyticsChangePassForm?.submitted"
              fieldName="Current Password"
            ></sfl-error-msg>
          </div>
        </div>
        <div class="form-group row">
          <label for="input-newPassword" class="label mb-0 col-form-label">New Password<span class="ms-1 text-danger">*</span></label>
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              [(ngModel)]="qeAnalyticsPasswordObj.newPassword"
              #newPassword="ngModel"
              name="newPassword"
              type="password"
              id="input-newPassword"
              class="form-control"
              required
            />
            <sfl-error-msg
              [control]="newPassword"
              [isFormSubmitted]="qeAnalyticsChangePassForm?.submitted"
              fieldName="New Password"
            ></sfl-error-msg>
          </div>
        </div>
        <div class="form-group row">
          <label for="input-confirmPassword" class="label mb-0 col-form-label"
            >Confirm Password<span class="ms-1 text-danger">*</span></label
          >
          <div class="mb-2">
            <input
              nbInput
              fullWidth
              name="confirmPassword"
              type="password"
              id="input-confirmPassword"
              [(ngModel)]="qeAnalyticsPasswordObj.confirmPassword"
              [NeedSameValue]="qeAnalyticsPasswordObj.newPassword"
              #confirmPassword="ngModel"
              class="form-control"
              required
            />
            <sfl-error-msg
              [control]="confirmPassword"
              [isFormSubmitted]="qeAnalyticsChangePassForm?.submitted"
              fieldName="Confirm Password"
              fromSameValueFieldName="New Password"
            ></sfl-error-msg>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary" [disabled]="qeAnalyticsChangePassForm.invalid">Continue</button>
      </div>
    </form>
  </div>
</ng-template>
