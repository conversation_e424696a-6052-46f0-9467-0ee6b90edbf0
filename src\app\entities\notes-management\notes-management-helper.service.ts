import { Injectable } from '@angular/core';
import { NotesEntityType } from './notes-management.model';
import { NotesManagementService } from './notes-management.service';

@Injectable({
  providedIn: 'root'
})
export class NotesManagementHelperService {
  constructor(private readonly notesManagementService: NotesManagementService) {}

  getAllNotes(entityTypeId: NotesEntityType, options) {
    switch (entityTypeId) {
      case NotesEntityType.SITES:
        return this.notesManagementService.getAllSiteNotes(entityTypeId, options);
      case NotesEntityType.CUSTOMERS:
      case NotesEntityType.PORTFOLIO:
      case NotesEntityType.CM_TICKETS:
        return this.notesManagementService.getAllNotes(entityTypeId, options);
      default:
        return;
    }
  }

  deleteNoteById(entityTypeId: NotesEntityType, noteId: number) {
    switch (entityTypeId) {
      case NotesEntityType.SITES:
        return this.notesManagementService.deleteSiteNoteById(entityTypeId, noteId);
      case NotesEntityType.CUSTOMERS:
      case NotesEntityType.PORTFOLIO:
      case NotesEntityType.CM_TICKETS:
        return this.notesManagementService.deleteNoteById(entityTypeId, noteId);
      default:
        return;
    }
  }

  getNoteById(entityTypeId: NotesEntityType, noteId: number) {
    switch (entityTypeId) {
      case NotesEntityType.SITES:
        return this.notesManagementService.getSiteNoteById(entityTypeId, noteId);
      case NotesEntityType.CUSTOMERS:
      case NotesEntityType.PORTFOLIO:
      case NotesEntityType.CM_TICKETS:
        return this.notesManagementService.getNoteById(entityTypeId, noteId);
      default:
        return;
    }
  }

  addUpdateNote(entityTypeId: NotesEntityType, details) {
    switch (entityTypeId) {
      case NotesEntityType.SITES:
        return this.notesManagementService.addUpdateSiteNote(entityTypeId, details);
      case NotesEntityType.CUSTOMERS:
      case NotesEntityType.PORTFOLIO:
      case NotesEntityType.CM_TICKETS:
        return this.notesManagementService.addUpdateNote(entityTypeId, details);
      default:
        return;
    }
  }

  getNoteTagList(entityTypeId: NotesEntityType) {
    switch (entityTypeId) {
      case NotesEntityType.SITES:
      case NotesEntityType.CUSTOMERS:
      case NotesEntityType.PORTFOLIO:
      case NotesEntityType.CM_TICKETS:
        return this.notesManagementService.getNoteTagList({ isCustomerFacing: false });
      default:
        return;
    }
  }
}
