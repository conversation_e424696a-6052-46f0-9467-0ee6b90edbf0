import { Injectable } from '@angular/core';
import { QE_MENU_MODULE_ENUM, QE_MENU_MODULE_NAME_ENUM } from '../../../@shared/enums/qe-menu.enum';
import { QEMenuModuleType } from '../models/qe-analytics.model';
import { QEAnalyticsService } from './qe-analytics.service';

@Injectable({
  providedIn: 'root'
})
export class QEAnalyticsGatheringService {
  qeMenuModuleList: QEMenuModuleType[] = [];

  constructor(private readonly qeAnalyticsService: QEAnalyticsService) {}

  setQEMenuModuleList(): void {
    const qeMenuModuleList =
      this.qeMenuModuleList && this.qeMenuModuleList.length > 0
        ? this.qeMenuModuleList
        : this.qeAnalyticsService.setQEMenuModuleListWithEnumMapping();
    this.qeMenuModuleList = qeMenuModuleList;
  }

  makeClickEventOnMenuItem(
    menuItemEnum: QE_MENU_MODULE_ENUM,
    parentItemEnum: QE_MENU_MODULE_ENUM,
    menuItemName: string,
    parentItemName: string
  ): void {
    const qeMenuModuleItem = this.qeMenuModuleList.find(
      item =>
        (item.menuUniqueId === menuItemEnum || item.menuName === menuItemName) &&
        (item.menuParentId === parentItemEnum || item.parentName === parentItemName)
    );
    console.log(qeMenuModuleItem);
    console.log(`Menu item clicked: ${menuItemName}, Parent item: ${parentItemName}`);
  }

  captureQEAnalyticsItemEnum(qeAnalyticsItemEnum: QE_MENU_MODULE_ENUM, qeAnalyticsParentItemEnum: QE_MENU_MODULE_ENUM): void {
    this.setQEMenuModuleList();
    const qeAnalyticsItemTitle = QE_MENU_MODULE_NAME_ENUM[qeAnalyticsItemEnum];
    const qeAnalyticsParentItemTitle = QE_MENU_MODULE_NAME_ENUM[qeAnalyticsParentItemEnum];
    this.makeClickEventOnMenuItem(qeAnalyticsItemEnum, qeAnalyticsParentItemEnum, qeAnalyticsItemTitle, qeAnalyticsParentItemTitle);
  }
}
