import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { FileListPaginationParams } from '../../ticket-management/ticket.model';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { NotesAddEditComponent } from '../notes-add-edit/notes-add-edit.component';
import { ConfirmDialogComponent } from '../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { AlertService } from '../../../@shared/services';
import { StorageService } from '../../../@shared/services/storage.service';
import {
  NotesAddEdit,
  NotesAddEditClass,
  NotesEntityName,
  NotesEntityType,
  NotesListingResponse,
  NotesModuleNameForShowUp
} from '../notes-management.model';
import { NotesManagementHelperService } from '../notes-management-helper.service';

@Component({
  selector: 'sfl-notes-listing',
  templateUrl: './notes-listing.component.html',
  styleUrls: ['./notes-listing.component.scss']
})
export class NotesListingComponent implements OnInit, OnChanges, OnDestroy {
  @Input() entityId: number = 0;
  @Input() entityTypeId: NotesEntityType = NotesEntityType.NONE;
  @Input() entityTypeName: string = NotesEntityName[NotesEntityType.NONE];
  @Input() isEntityEditMode = false;
  @Input() isEntityViewMode = false;
  @Input() isEntityCreateMode = false;
  @Output() notesListingLoadingEvent: EventEmitter<boolean> = new EventEmitter(false);
  subscription: Subscription = new Subscription();
  notesSearch: string;
  notesListingResponse: NotesListingResponse = new NotesListingResponse([], 0);
  notesSearchModelChanged = new Subject<string>();
  notesSortOptionList = {
    NoteName: 'asc',
    UpdatedDate: 'asc'
  };
  notesFilterObject: { page: number; items: number; sortBy: string; direction: string } = {
    page: 0,
    items: 10,
    sortBy: '',
    direction: ''
  };
  modalRef: BsModalRef;
  notesPaginationParams: FileListPaginationParams = new FileListPaginationParams();
  userRoles: string;
  selectedNote: NotesAddEdit;

  constructor(
    private readonly modalService: BsModalService,
    private readonly notesManagementHelperService: NotesManagementHelperService,
    private readonly alertService: AlertService,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.userRoles = this.storageService.get('user').authorities;

    this.setEntityIdWithGetAllNotes();

    this.notesSearchModelChanged.pipe(debounceTime(1000)).subscribe(() => {
      this.getAllNotes();
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['entityId']) {
      if (this.entityTypeId === NotesEntityType.CM_TICKETS) {
        this.setEntityIdWithGetAllNotes();
      }
    }
  }

  setEntityIdWithGetAllNotes(): void {
    this.entityId = Number(this.entityId);
    if (this.entityId) {
      this.getAllNotes();
    }
  }

  getAllNotes(requestParams = null): void {
    this.notesListingLoadingEvent.emit(true);

    const options = {
      sortBy: requestParams ? requestParams?.sortBy : this.notesFilterObject.sortBy,
      direction: requestParams ? requestParams?.direction : this.notesFilterObject.direction,
      page: requestParams ? requestParams.page : this.notesFilterObject.page,
      itemsCount: requestParams ? requestParams.itemsCount : this.notesFilterObject.items,
      search: this.notesSearch ? this.notesSearch : '',
      entityId: this.entityId,
      entityTypeId: this.entityTypeId,
      entityTypeName: this.entityTypeName
    };

    this.subscription.add(
      this.notesManagementHelperService.getAllNotes(this.entityTypeId, options).subscribe({
        next: res => {
          this.notesListingResponse = new NotesListingResponse(res.notes, res.totalNotes);
          this.notesListingLoadingEvent.emit(false);
        },
        error: e => {
          this.notesListingLoadingEvent.emit(false);
        }
      })
    );
  }

  getNote(noteId: number): void {
    this.notesListingLoadingEvent.emit(true);
    this.subscription.add(
      this.notesManagementHelperService.getNoteById(this.entityTypeId, noteId).subscribe({
        next: res => {
          this.selectedNote = res;
          this.notesListingLoadingEvent.emit(false);
        },
        error: e => {
          this.notesListingLoadingEvent.emit(false);
        }
      })
    );
  }

  openAddNoteSidePanel(mode: boolean, noteDetails: NotesAddEdit): void {
    const notesEntityDetails = new NotesAddEditClass({
      ...noteDetails,
      entityId: this.entityId,
      entityTypeId: this.entityTypeId
    });

    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isNotesViewMode: mode,
        notesEntityDetails: notesEntityDetails,
        entityTypeName: this.entityTypeName,
        entityTypeId: this.entityTypeId,
        entityId: this.entityId,
        isEntityViewMode: this.isEntityCreateMode ? false : true
      }
    };
    this.modalRef = this.modalService.show(NotesAddEditComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.getAllNotes();
      }
    });
  }

  viewNote(noteDetails: NotesAddEdit): void {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-lg modal-dialog-right',
      initialState: {
        isNotesViewMode: true,
        notesEntityDetails: noteDetails,
        entityTypeName: this.entityTypeName,
        entityTypeId: this.entityTypeId,
        entityId: this.entityId,
        isEntityViewMode: this.isEntityCreateMode ? false : true
      }
    };
    this.modalRef = this.modalService.show(NotesAddEditComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(res => {
      if (res) {
        this.getAllNotes();
      }
    });
  }

  deleteNote(noteId: number): void {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: {
        message: `Are you sure you want to remove this note?`
      }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.notesListingLoadingEvent.emit(true);
        this.subscription.add(
          this.notesManagementHelperService.deleteNoteById(this.entityTypeId, noteId).subscribe({
            next: () => {
              this.notesListingLoadingEvent.emit(false);
              this.alertService.showSuccessToast(`${NotesModuleNameForShowUp[this.entityTypeId]} Note deleted successfully`);
              this.notesFilterObject.page = 0;
              this.notesPaginationParams.currentPage = 1;
              this.getAllNotes();
            },
            error: () => {
              this.notesListingLoadingEvent.emit(false);
            }
          })
        );
      }
    });
  }

  noteSearchChanged(): void {
    this.notesSearchModelChanged.next(null);
  }

  sortNotes(sortBy: string, changeSort: string): void {
    if (changeSort === 'asc') {
      changeSort = 'desc';
    } else {
      changeSort = 'asc';
    }
    this.notesSortOptionList[sortBy] = changeSort;
    this.notesFilterObject.sortBy = sortBy;
    this.notesFilterObject.direction = changeSort;
    this.getAllNotes();
  }

  onNotesPageChange(obj) {
    this.notesPaginationParams.currentPage = obj;
    this.notesFilterObject.items = this.notesPaginationParams.itemsCount;
    this.notesFilterObject.page = obj - 1;
    const params = {
      page: this.notesFilterObject.page,
      itemsCount: this.notesFilterObject.items,
      sortBy: this.notesFilterObject.sortBy,
      direction: this.notesFilterObject.direction
    };
    this.getAllNotes(params);
  }

  onNoteChangeSize() {
    this.notesPaginationParams.itemsCount = Number(this.notesPaginationParams.pageSize);
    this.notesFilterObject.items = this.notesPaginationParams.itemsCount;
    this.notesFilterObject.page = this.notesPaginationParams.currentPage = 0;
    const params = {
      page: 0,
      itemsCount: this.notesPaginationParams.itemsCount,
      sortBy: this.notesFilterObject.sortBy,
      direction: this.notesFilterObject.direction
    };
    this.getAllNotes(params);
  }

  getTagList(tags: []): string {
    return tags.join(', ');
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
