<div class="modal-content">
  <div class="modal-header" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
    <div class="col-md-4">
      <h4 class="modal-title">Generate Report</h4>
    </div>
    <div class="col-sm-8 text-end">
      <button nbButton (click)="generateSiteMapReport(true)" class="me-2" [disabled]="loading || isAllSiteMapNotReviewed">
        Generate Report
      </button>
      <button type="button" class="close" aria-label="Close" (click)="_bsModalRef.hide()">
        <span aria-hidden="true"><i class="fa-solid fa-xmark fa-xl"></i></span>
      </button>
    </div>
  </div>
  <div class="modal-body">
    <div class="row">
      <ng-container *ngIf="siteImages && siteImages.length; else noSiteImages">
        <div class="col-sm-12" *ngIf="siteImages">
          <label class="text-danger"
            >You have not reviewed site maps after changes in the report.<br />
            Please review by clicking on the "Click To Review" button and save each.
          </label>
        </div>
        <div class="row col-sm-12">
          <div class="col-sm-3 mb-3" *ngFor="let item of siteImages">
            <div class="site-layout-images">
              <div class="layout-thumbnail my-2">
                <img
                  [src]="item?.imageURL"
                  alt="Non Conformance Image"
                  class="text-center siteImage"
                  onError="this.src='assets/images/no-image-found.jpg'"
                />
              </div>

              <div class="layout-review-btn">
                <ng-container *ngIf="!item?.isReviewed">
                  <button nbButton status="primary" size="medium" type="button" (click)="imageReview(siteImages, item)" class="">
                    Click to review
                  </button>
                </ng-container>
                <ng-container *ngIf="item?.isReviewed">
                  <button nbButton status="basic" type="button" class="" style="cursor: not-allowed">Reviewed</button>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #noSiteImages>
        <div class="col-sm-12">
          <label class="text-center w-100">No site images available to review.</label>
        </div>
      </ng-template>
    </div>
  </div>
</div>
