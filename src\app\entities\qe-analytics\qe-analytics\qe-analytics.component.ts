import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonFilter } from '../../../@shared/components/filter/common-filter.model';
import { Subscription } from 'rxjs';
import { FILTER_PAGE_NAME, FILTER_SECTION_ENUM, FilterDetails } from '../../../@shared/components/filter/filter.model';
import { AppConstants } from '../../../@shared/constants';
import { analyticsCategories } from '../models/qe-analytics.model';
import { StorageService } from '../../../@shared/services/storage.service';

@Component({
  selector: 'sfl-qe-analytics',
  templateUrl: './qe-analytics.component.html',
  styleUrls: ['./qe-analytics.component.scss']
})
export class QEAnalyticsComponent implements OnInit, OnDestroy {
  loading = false;
  currentPage = 1;
  isFilterDisplay = false;
  isApiErrorFilter = true;
  filterModel: CommonFilter = new CommonFilter();
  subscription: Subscription = new Subscription();
  filterDetails: FilterDetails = new FilterDetails();
  viewPage = FILTER_PAGE_NAME.ADMIN_QE_ANALYTICS;
  viewFilterSection = 'qeAnalyticsFilterSection';
  total = 10;
  pageSize = AppConstants.rowsPerPage;
  fullDateFormat = AppConstants.fullDateFormat;
  sortOptionList = {
    auditId: 'asc',
    auditDate: 'asc',
    datasource: 'asc',
    customerPortfolio: 'asc',
    siteName: 'asc',
    frequency: 'asc',
    errorCode: 'asc'
  };
  analyticsCategories = analyticsCategories;
  maxItems = 0;

  constructor(private readonly storageService: StorageService) {}

  ngOnInit(): void {
    const passwordProtected = this.storageService.get(AppConstants.qeAnalyticsPasswordAuthKey);
    if (passwordProtected) {
      this.storageService.clear(AppConstants.qeAnalyticsPasswordAuthKey);
    }

    this.maxItems = Math.max(...this.analyticsCategories.map(cat => cat.items.length));
    const filter = this.storageService.get(this.viewPage);
    const filterSection = this.storageService.get(this.viewFilterSection);
    this.isFilterDisplay = filterSection;
    if (filter) {
      this.filterModel = filter;
    }

    this.initFilterDetails();
  }

  initFilterDetails(): void {
    this.filterDetails.filter_section_name = this.viewFilterSection;
    this.filterDetails.page_name = this.viewPage;
    this.filterDetails.api = [];
    this.filterDetails.filterSectionEnum = FILTER_SECTION_ENUM.ADMIN_QE_ANALYTICS;
    let filterItem = JSON.parse(JSON.stringify(AppConstants.FILTERS));
    filterItem.USER_TYPE.show = true;
    filterItem.USER_TYPE.multi = true;
    filterItem.USER.show = true;
    filterItem.QE_MODULES.show = true;
    filterItem.QE_DATE_DURATION.show = true;

    this.filterDetails.filter_item = filterItem;
  }

  get maxItemsArray(): number[] {
    return Array.from({ length: this.maxItems }, (_, i) => i);
  }

  refreshList(filterParams: CommonFilter) {
    this.currentPage = filterParams.page;
    console.log(filterParams);
    // this.getApiErrorLogDetail(true, filterParams);
  }

  ngOnDestroy(): void {
    this.storageService.clear(AppConstants.qeAnalyticsPasswordAuthKey);
  }
}
