import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, Renderer2, Template<PERSON><PERSON>, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subject, Subscription } from 'rxjs';
import { QEAnalytics, QEAnalyticsPasswordObj } from '../models/qe-analytics.model';
import { QEAnalyticsPasswordOperationEnum, QEAnalyticsPasswordAuthEnum } from '../models/qe-analytics.enum';
import { Router } from '@angular/router';
import { StorageService } from '../../../@shared/services/storage.service';
import { AppConstants } from '../../../@shared/constants';
import { QEAnalyticsService } from '../services/qe-analytics.service';

@Component({
  selector: 'sfl-qe-analytics-password',
  templateUrl: './qe-analytics-password.component.html',
  styleUrls: ['./qe-analytics-password.component.scss']
})
export class QEAnalyticsPasswordComponent implements OnInit, OnDestroy {
  @ViewChild('qeAnalyticsPasswordButton', { static: true }) qeAnalyticsPasswordButton;

  private subscription = new Subscription();
  modalRef: BsModalRef;
  qeAnalyticsPasswordObj: QEAnalyticsPasswordObj = new QEAnalyticsPasswordObj();
  pwdOperation = QEAnalyticsPasswordOperationEnum;
  loading = false;

  constructor(
    private readonly modalService: BsModalService,
    private readonly renderer: Renderer2,
    private readonly router: Router,
    private readonly storageService: StorageService,
    private readonly qeAnalyticsService: QEAnalyticsService
  ) {}

  ngOnInit(): void {
    setTimeout(() => {
      this.qeAnalyticsPasswordButton.nativeElement.click();
      return;
    }, 1);
  }

  openModal(template: TemplateRef<any>, pwdOperation: QEAnalyticsPasswordOperationEnum): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }

    const ngModalOptions: ModalOptions = {
      backdrop: true,
      ignoreBackdropClick: true,
      keyboard: false,
      animated: true,
      class: 'qe-analytics-password-modal-dialog'
    };

    this.modalRef = this.modalService.show(template, ngModalOptions);

    this.qeAnalyticsPasswordObj = new QEAnalyticsPasswordObj(pwdOperation);

    this.addCustomClassToModal();
  }

  private addCustomClassToModal(): void {
    const modalElement = document.querySelector('.modal');
    if (modalElement?.querySelector('.qe-analytics-password-modal-dialog')) {
      this.renderer.setStyle(modalElement, 'z-index', '99999');
    }
  }

  onPasswordVerified(isRouteActivated: boolean): void {
    this.modalRef.hide();
    if (isRouteActivated) {
      this.storageService.set(AppConstants.qeAnalyticsPasswordAuthKey, QEAnalyticsPasswordAuthEnum.USER_AUTHENTICATED);
      this.router.navigate(['entities/admin/analytics']);
    } else {
      this.router.navigate(['entities/dashboard']);
    }
  }

  resetQEAnalyticsPassword(pwdOperation: QEAnalyticsPasswordOperationEnum): void {
    const qeAnalyticsPasswordObj = new QEAnalyticsPasswordObj(pwdOperation);
    this.loading = true;
    this.subscription.add(
      this.qeAnalyticsService.resetQEAnalyticsPassword(qeAnalyticsPasswordObj).subscribe({
        next: res => {
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  checkAndUpdateQEAnalyticsPassword(qeAnalyticsPasswordObj: QEAnalyticsPasswordObj): void {
    this.loading = true;
    this.subscription.add(
      this.qeAnalyticsService.checkAndUpdateQEAnalyticsPassword(qeAnalyticsPasswordObj).subscribe({
        next: res => {
          if (res) {
            this.onPasswordVerified(true);
          } else {
            this.onPasswordVerified(false);
          }
          this.loading = false;
        },
        error: e => {
          this.loading = false;
        }
      })
    );
  }

  onSubmit(qeAnalyticsPasswordForm: NgForm): void {
    if (qeAnalyticsPasswordForm.form.valid) {
      this.checkAndUpdateQEAnalyticsPassword(this.qeAnalyticsPasswordObj);
    } else {
      qeAnalyticsPasswordForm.form.markAllAsTouched();
    }
  }

  ngOnDestroy(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.subscription.unsubscribe();
  }
}
