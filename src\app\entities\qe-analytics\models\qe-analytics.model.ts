import { QE_MENU_MODULE_ENUM } from '../../../@shared/enums/qe-menu.enum';
import { QEAnalyticsPasswordOperationEnum } from './qe-analytics.enum';
export interface QEAnalytics {}

export interface QEAnalyticsTableRes {}

export class QEAnalyticsPasswordObj {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  pwdOperation: QEAnalyticsPasswordOperationEnum;

  constructor(pwdOperation: QEAnalyticsPasswordOperationEnum = QEAnalyticsPasswordOperationEnum.PASSWORD) {
    this.pwdOperation = pwdOperation;
  }
}

export interface AnalyticsCategory {
  name: string;
  items: { label: string; percent: string; childrens?: { label: string; percent: string }[] }[];
}

export const analyticsCategories: AnalyticsCategory[] = [
  {
    name: 'CM',
    items: [
      { label: 'Dashboard', percent: '32%' },
      { label: 'All Tickets', percent: '18%' },
      { label: 'Ticket Audit Report', percent: '9%' },
      { label: 'Exclusion Report', percent: '6%' },
      { label: 'Billing Report', percent: '6%' },
      { label: 'Truck Roll Report', percent: '6%' },
      { label: 'Map Report', percent: '4%' },
      { label: 'Site Info', percent: '8%' },
      { label: 'Dashboard', percent: '8%' },
      { label: 'Site Info', percent: '8%' },
      { label: 'Site hoard', percent: '7%' },
      { label: 'Equipment', percent: '6%' }
    ]
  },
  {
    name: 'PM',
    items: [
      { label: 'Dashboard', percent: '6%' },
      { label: 'Scope', percent: '6%' },
      { label: 'Reports', percent: '8%' },
      { label: 'Site audit', percent: '6%' },
      { label: 'Nonconformance', percent: '3%' },
      { label: 'Performance', percent: '3%' },
      { label: 'Sne', percent: '' },
      { label: 'Site Check-In', percent: '2%' },
      { label: 'Site Audit-JHA', percent: '2%' }
    ]
  },
  {
    name: 'Availability',
    items: [
      { label: 'Dashboard', percent: '6%' },
      { label: 'Scope', percent: '6%' },
      { label: 'Reports', percent: '8%' },
      { label: 'Site audit', percent: '6%' },
      { label: 'Nonconformance', percent: '3%' },
      { label: 'Performance', percent: '3%' }
    ]
  },
  {
    name: 'Safety',
    items: [
      { label: 'JHA', percent: '2%' },
      { label: 'Site Check-In', percent: '2%' },
      { label: 'JHaza', percent: '2%' },
      { label: 'Reports', percent: '6%' },
      { label: 'Region mapping', percent: '4%' },
      { label: 'Services', percent: '3%' },
      { label: 'Contracts', percent: '%' },
      { label: 'Custom forms', percent: '3%' },
      {
        label: 'Settings',
        percent: '',
        childrens: [
          { label: 'General Info', percent: '2%' },
          { label: 'Work Type', percent: '2%' },
          { label: 'Work Step', percent: '2%' },
          { label: 'Hazard', percent: '2%' },
          { label: 'Barrier', percent: '2%' }
        ]
      }
    ]
  },
  {
    name: 'Admin',
    items: [
      { label: 'Users', percent: '14%' },
      { label: 'Data source mapping', percent: '5%' },
      { label: 'API error log', percent: '6%' },
      { label: 'Report scheduler', percent: '5%' },
      { label: 'Customer API gateway', percent: '5%' },
      { label: 'API gateway dashboard', percent: '4%' },
      { label: 'Re-Fetch scheduler', percent: '2%' },
      { label: 'Email log', percent: '4%' }
    ]
  }
];

export class QEMenuModuleType {
  public menuId: number;
  public menuName: string;
  public parentName: string;
  public parentId: number;
  public menuLevelOrder: number;
  public menuUniqueId: QE_MENU_MODULE_ENUM;
  public menuParentId: QE_MENU_MODULE_ENUM;
}
