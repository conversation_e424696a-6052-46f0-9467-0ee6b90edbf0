<div class="notes-listing">
  <div class="d-flex align-items-center justify-content-end mb-3">
    <input
      *ngIf="isEntityEditMode"
      nbInput
      name="noteSearch"
      id="input-noteSearch"
      [(ngModel)]="notesSearch"
      #noteSearchModel="ngModel"
      class="form-control me-2"
      placeholder="Search Notes"
      (ngModelChange)="noteSearchChanged()"
    />
    <button
      *ngIf="userRoles[0] !== 'customer' && isEntityEditMode"
      nbButton
      status="primary"
      size="small"
      type="button"
      id="addNotes"
      class="me-2"
      (click)="openAddNoteSidePanel(false, {})"
    >
      <span class="d-flex"><em class="pi pi-plus me-2"></em>Add Note</span>
    </button>
  </div>
  <div class="wrapper-notes-listing">
    <div id="fixed-table" setTableHeight [isFilterDisplay]="true" class="col-12 table-responsive table-card-view">
      <table class="table table-hover table-bordered" aria-describedby="Note List">
        <thead *ngIf="notesListingResponse?.notes?.length">
          <tr>
            <th (click)="sortNotes('NoteName', notesSortOptionList['NoteName'])" id="NoteName">
              Note Name
              <span
                class="fa cursor-pointer ms-auto"
                [ngClass]="{
                  'fa-arrow-up': notesSortOptionList['NoteName'] === 'desc',
                  'fa-arrow-down': notesSortOptionList['NoteName'] === 'asc',
                  'icon-selected': notesFilterObject.sortBy === 'NoteName'
                }"
              ></span>
            </th>
            <th id="Tags">Tags</th>
            <th id="Summary">Summary</th>
            <th id="UpdatedBy">Updated By</th>
            <th (click)="sortNotes('UpdatedDate', notesSortOptionList['UpdatedDate'])" id="UpdatedDate">
              Updated Date
              <span
                class="fa cursor-pointer ms-auto"
                [ngClass]="{
                  'fa-arrow-up': notesSortOptionList['UpdatedDate'] === 'desc',
                  'fa-arrow-down': notesSortOptionList['UpdatedDate'] === 'asc',
                  'icon-selected': notesFilterObject.sortBy === 'UpdatedDate'
                }"
              ></span>
            </th>
            <th id="Action" class="text-center">Action</th>
          </tr>
        </thead>
        <tbody *ngIf="!isEntityCreateMode">
          <ng-container *ngIf="notesListingResponse?.notes?.length">
            <tr
              *ngFor="
                let noteItem of notesListingResponse?.notes
                  | paginate
                    : {
                        id: 'notesListingControl',
                        itemsPerPage: notesPaginationParams.itemsCount,
                        currentPage: notesPaginationParams.currentPage,
                        totalItems: notesListingResponse?.totalNotes
                      }
              "
            >
              <td data-title="Note Name">
                <div class="d-flex align-items-center">
                  {{ noteItem.noteName }}
                </div>
              </td>
              <td data-title="tags">
                <ng-container *ngIf="noteItem?.tagsName?.length">
                  <span class="tag-info-badge fw-bold" *ngFor="let tagName of noteItem?.tagsName | slice : 0 : 5">
                    <span class="px-2">
                      {{ tagName }}
                    </span>
                  </span>
                  <span class="tag-info-badge fw-bold" nbTooltipStatus="primary" [nbTooltip]="getTagList(noteItem?.tagsName)">
                    {{ noteItem?.tagsName?.length > 5 ? '+' + (noteItem?.tagsName?.length - 5) + ' More' : '' }}
                  </span>
                </ng-container>
                <ng-container *ngIf="!noteItem?.tagsName?.length">N/A</ng-container>
              </td>
              <td data-title="Summary">
                <div
                  *ngIf="noteItem.summary"
                  nbTooltip="{{ noteItem.summary.length > 600 ? (noteItem.summary | slice : 0 : 600) + '...' : noteItem.summary }}"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="primary"
                >
                  <sfl-read-more [content]="noteItem.summary"></sfl-read-more>
                </div>
                <span *ngIf="!noteItem.summary">N/A</span>
              </td>
              <td data-title="Updated By">{{ noteItem.updatedByName }}</td>
              <td data-title="Updated Date">{{ noteItem.updatedDate | date : fullDateFormat }}</td>
              <td data-title="Action" class="text-center">
                <div class="d-flex align-items-center justify-content-center">
                  <ng-container *ngIf="isEntityEditMode">
                    <em class="fa fa-edit text-primary cursor-pointer me-3" (click)="openAddNoteSidePanel(false, noteItem)"></em>
                    <em
                      *ngIf="userRoles[0] !== 'customer'"
                      class="fa fa-trash text-danger cursor-pointer"
                      (click)="deleteNote(noteItem.id)"
                    ></em>
                  </ng-container>
                  <em
                    *ngIf="!isEntityEditMode && !isEntityCreateMode"
                    class="fa fa-eye text-primary cursor-pointer"
                    (click)="viewNote(noteItem)"
                  ></em>
                </div>
              </td>
            </tr>
          </ng-container>
        </tbody>
      </table>
      <ng-container
        *ngIf="
          (!notesListingResponse?.notes?.length && !isEntityCreateMode) || (!notesListingResponse?.notes?.length && isEntityCreateMode)
        "
      >
        <p class="no-record text-center">No Data Found</p>
      </ng-container>
    </div>
    <div class="mt-2 d-md-flex align-items-center" *ngIf="notesListingResponse?.notes?.length && !isEntityCreateMode">
      <div class="d-flex align-items-center">
        <label class="mb-0">Items per page: </label>
        <ng-select
          class="ms-2"
          [(ngModel)]="notesPaginationParams.pageSize"
          [clearable]="false"
          [searchable]="false"
          (change)="onNoteChangeSize()"
          name="notesPageSize"
          #notesPageSize="ngModel"
          appendTo="body"
          id="notesListingControl"
        >
          <ng-option value="5">5</ng-option>
          <ng-option value="10">10</ng-option>
          <ng-option value="50">50</ng-option>
          <ng-option value="100">100</ng-option>
        </ng-select>
      </div>
      <strong class="ms-md-3">Total: {{ notesListingResponse?.totalNotes }}</strong>
      <div class="ms-md-auto ms-sm-0">
        <pagination-controls
          id="notesListingControl"
          (pageChange)="onNotesPageChange($event)"
          class="paginate notes-listing"
        ></pagination-controls>
      </div>
    </div>
  </div>
</div>
