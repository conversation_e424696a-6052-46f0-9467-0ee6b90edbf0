export enum NotesEntityType {
  NONE = 0,
  SITES = 1,
  CUSTOMERS = 2,
  PORTFOL<PERSON> = 3,
  CM_TICKETS = 4
}

export const NotesEntityName = {
  [NotesEntityType.NONE]: 'none',
  [NotesEntityType.SITES]: 'sites',
  [NotesEntityType.CUSTOMERS]: 'customers',
  [NotesEntityType.PORTFOLIO]: 'portfolio',
  [NotesEntityType.CM_TICKETS]: 'cmTickets'
};

export const NotesModuleNameForShowUp = {
  [NotesEntityType.NONE]: 'none',
  [NotesEntityType.SITES]: 'Site',
  [NotesEntityType.CUSTOMERS]: 'Customer',
  [NotesEntityType.PORTFOLIO]: 'Portfolio',
  [NotesEntityType.CM_TICKETS]: 'Ticket'
};

export const NotesModuleEntityIdKeyName = {
  [NotesEntityType.NONE]: '',
  [NotesEntityType.SITES]: 'siteId',
  [NotesEntityType.CUSTOMERS]: 'customerId',
  [NotesEntityType.PORTFOLIO]: 'portfolioId',
  [NotesEntityType.CM_TICKETS]: 'ticketId'
};

export class NotesListingResponse {
  totalNotes: number;
  notes: NotesAddEdit[];
  constructor(notes: NotesAddEdit[], totalNotes: number) {
    this.notes = notes;
    this.totalNotes = totalNotes;
  }
}

export interface NotesAddEdit {
  id: number;
  entityId: number;
  noteName: string;
  tags: number[];
  tagsName: string[];
  summary: string;
  updatedBy: number;
  updatedByName: string;
  updatedDate: string;
  entityTypeId: NotesEntityType;
  entityTypeName: string;
}

export class NotesAddEditClass implements NotesAddEdit {
  id: number;
  entityId: number;
  noteName: string;
  tags: number[];
  tagsName: string[];
  summary: string;
  updatedBy: number;
  updatedByName: string;
  updatedDate: string;
  entityTypeId: NotesEntityType;
  entityTypeName: string;
  constructor(notesEntityDetails: Partial<NotesAddEdit>) {
    this.id = notesEntityDetails?.id ?? 0;
    this.entityId = notesEntityDetails?.entityId ?? 0;
    this.noteName = notesEntityDetails?.noteName ?? '';
    this.tags = notesEntityDetails?.tags ?? [];
    this.tagsName = notesEntityDetails?.tagsName ?? [];
    this.summary = notesEntityDetails?.summary ?? '';
    this.updatedBy = notesEntityDetails?.updatedBy ?? 0;
    this.updatedByName = notesEntityDetails?.updatedByName ?? '';
    this.updatedDate = notesEntityDetails?.updatedDate ?? '';
    this.entityTypeId = notesEntityDetails?.entityTypeId ?? NotesEntityType.NONE;
    this.entityTypeName = NotesEntityName[this.entityTypeId];
  }
}

export interface SiteNotes extends NotesAddEdit {
  siteId: number;
}

export interface SiteNotesResponse {
  totalSiteNotes: number;
  siteNotes: SiteNotes[];
}

export interface CustomerNotes extends NotesAddEdit {
  customerId: number;
}

export interface PortfolioNotes extends NotesAddEdit {
  portfolioId: number;
}

export interface TicketNotes extends NotesAddEdit {
  ticketId: number;
}
