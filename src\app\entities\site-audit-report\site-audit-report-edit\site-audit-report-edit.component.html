<nb-card class="reports" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header class="d-flex align-items-center">
    <h6 class="w-100">{{ reportMasterList?.reportTitle }}</h6>
    <div class="d-flex align-items-center w-100">
      <div class="ms-auto d-flex button_list" *ngIf="reportMasterList?.reportPdf === null && reportMasterList?.reportPpt === null">
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="!viewdeletetedbutton"
          [disabled]="loading || (reportMasterList?.customerName && reportMasterList?.portfolioName && reportMasterList?.siteName)"
          class="ms-1 ms-sm-2"
          (click)="saveChanges()"
        >
          <span class="d-none d-lg-inline-block">Save Changes</span>
          <i class="d-inline-block d-lg-none fa fa-floppy-o"></i>
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="!viewdeletetedbutton"
          [disabled]="loading || reportMasterList?.reports[0].jhaMap.length === 0"
          class="ms-1 ms-sm-2"
          (click)="previewReportStart(reportId, template)"
        >
          <span class="d-none d-lg-inline-block"> Preview Report</span>
          <i class="d-inline-block d-lg-none fa-regular fa-eye"></i>
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="!viewdeletetedbutton"
          [disabled]="loading || reportMasterList?.reports[0].jhaMap.length === 0"
          class="ms-1 ms-sm-2"
          (click)="generateReportStart(reportMasterList?.reports[0].reportGuid)"
        >
          <span class="d-none d-lg-inline-block"> <em class="fa fa-file-download download_icon me-2"></em> Generate Report</span>
          <i class="d-inline-block d-lg-none fa fa-file-download download_icon"></i>
        </button>
        <button nbButton status="basic" [disabled]="loading" (click)="onBackClick(viewdeletetedbutton)" size="medium" class="ms-1 ms-sm-2">
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
      </div>
      <div class="ms-auto d-flex button_list" *ngIf="reportMasterList?.reportPdf !== null || reportMasterList?.reportPpt !== null">
        <div class="d-flex align-items-center">
          <div class="ms-auto d-flex button_list text-center me-2">
            <fieldset class="p-2 border border-1 border-dark" *ngIf="!viewdeletetedbutton">
              <legend class="text-center fs-6">Download Report</legend>
              <button
                nbButton
                status="default"
                size="medium"
                type="button"
                (click)="downloadReport(reportId, reportMasterList?.siteName, reportMasterList?.workorderName)"
              >
                <em
                  class="fa fa-file-pdf download_icon download_pdf"
                  nbTooltip="Download PDF Report"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="danger"
                ></em>
              </button>
            </fieldset>
          </div>
          <div class="ms-auto d-flex button_list">
            <button nbButton status="basic" (click)="onBackClick(viewdeletetedbutton)" size="medium" class="btn-block me-2">
              <span class="d-none d-lg-inline-block">Back</span>
              <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
            </button>
            <button
              nbButton
              status="primary"
              size="medium"
              *ngIf="!viewdeletetedbutton"
              type="button"
              class="btn-block"
              [disabled]="reportMasterList?.customerName && reportMasterList?.portfolioName && reportMasterList?.siteName"
              (click)="saveChanges()"
            >
              <span class="d-none d-lg-inline-block">Save Changes</span>
              <i class="d-inline-block d-lg-none fa fa-floppy-o"></i>
            </button>
          </div>
        </div>
      </div></div
  ></nb-card-header>
  <nb-card-body>
    <div class="form-control-group">
      <div class="col-12 table-responsive">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th class="text-center" id="customerName">Customer Name</th>
              <th class="text-center" id="portfolioName">Portfolio Name</th>
              <th class="text-center" id="siteName">Site Name</th>
              <th class="text-center" id="workOrder">Report Name</th>
              <th class="text-center" id="totalReports">Total Reports</th>
              <th class="text-center" id="siteLayout">Site Layout</th>
              <th class="text-center" id="imageGallery">Image Gallery</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center">
                <label class="label">
                  <input
                    nbInput
                    fullWidth
                    [ngModel]="reportMasterList?.customerName"
                    class="form-control"
                    required
                    (ngModelChange)="onCPSModelChange($event, 'customerList')"
                    [nbAutocomplete]="cusAutoNgModel"
                  />
                  <nb-autocomplete #cusAutoNgModel>
                    <nb-option *ngFor="let item of clonedSiteAuditCPSListData.customerList" [value]="item">
                      {{ item }}
                    </nb-option>
                  </nb-autocomplete>
                </label>
              </td>
              <td class="text-center">
                <label class="label">
                  <input
                    nbInput
                    fullWidth
                    name="portfolioName"
                    [ngModel]="reportMasterList?.portfolioName"
                    class="form-control"
                    required
                    (ngModelChange)="onCPSModelChange($event, 'portfolioList')"
                    [nbAutocomplete]="portAutoNgModel"
                  />
                  <nb-autocomplete #portAutoNgModel>
                    <nb-option *ngFor="let item of clonedSiteAuditCPSListData.portfolioList" [value]="item">
                      {{ item }}
                    </nb-option>
                  </nb-autocomplete>
                </label>
              </td>
              <td class="text-center">
                <label class="label">
                  <input
                    nbInput
                    fullWidth
                    name="siteName"
                    [ngModel]="reportMasterList?.siteName"
                    class="form-control"
                    required
                    (ngModelChange)="getReportTitle($event); onCPSModelChange($event, 'siteList')"
                    [nbAutocomplete]="siteAutoNgModel"
                  />
                  <nb-autocomplete #siteAutoNgModel>
                    <nb-option *ngFor="let item of clonedSiteAuditCPSListData.siteList" [value]="item">
                      {{ item }}
                    </nb-option>
                  </nb-autocomplete>
                </label>
              </td>
              <td class="text-center">
                <label class="label">{{ reportMasterList?.reportTitle }}</label>
              </td>
              <td class="text-center">
                <label class="label">{{ reportMasterList?.reportCount }}</label>
              </td>
              <td>
                <div>
                  <a class="listgrid-icon px-1 text-primary view-image">
                    <em
                      class="fa fa-map-marker-alt"
                      nbTooltip="Site Map"
                      nbTooltipPlacement="top"
                      (click)="onUpload()"
                      nbTooltipStatus="primary"
                    ></em>
                  </a>
                </div>
              </td>
              <td class="text-center cursor-pointer">
                <a class="px-1 text-primary listgrid-icon" (click)="onImageGallery()">
                  <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <nb-tabset fullWidth>
      <nb-tab tabTitle="Job Hazard Analysis (JHA)">
        <sfl-site-audit-jha
          *ngIf="reportMasterList?.versionId === 2"
          [jhaDatas]="reportList"
          (descriptionWork)="(selectedItem)"
        ></sfl-site-audit-jha>
        <!-- <sfl-jobhazard *ngIf="reportMasterList?.versionId === 2" [jhaDatas]="reportList" (descriptionWork)="(selectedItem)"></sfl-jobhazard> -->
        <sfl-jhathree
          *ngIf="reportMasterList?.versionId === 3"
          [jhaDatas]="reportList"
          [jhaList]="jhaDetail"
          (reportDataModified)="reportDataModified($event)"
          (jhaThreeEvents)="onJhaThreeEventClicked($event)"
          [jhaListLoader]="jhaListLoader"
          [isCPSValueModified]="checkCPSValueModified()"
        ></sfl-jhathree>
      </nb-tab>
      <nb-tab tabTitle="General Images" class="text-capitalize">
        <sfl-site-audit-general-images [generalImagesList]="reportList"></sfl-site-audit-general-images>
      </nb-tab>
      <nb-tab tabTitle="Checklist">
        <sfl-site-audit-checklist [checklist]="reportList"></sfl-site-audit-checklist>
      </nb-tab>
      <nb-tab tabTitle="Non-Conformance">
        <sfl-site-audit-nonconformance
          [masterReportDataList]="reportList"
          (nCOrderChange)="nCOrderChangeSave = $event"
          (siteReportImagesCloseEvent)="getById(reportId, $event)"
        ></sfl-site-audit-nonconformance>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
</nb-card>
<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Preview</h4>
  </div>
  <div class="modal-body preview-body">
    <div #divLegalNoticeHeight [innerHTML]="previewHTML | safeHTML"></div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" #closeModalButton (click)="previewModalRef.hide()">Close</button>
  </div>
</ng-template>
