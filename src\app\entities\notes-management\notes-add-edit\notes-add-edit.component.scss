::ng-deep.modal-dialog-right {
  position: fixed;
  margin: auto;
  width: 600px;
  right: 0px;
  overflow-y: auto;
  height: 100%;
}

@media (max-width: 700px) {
  ::ng-deep.modal-dialog-right {
    width: 80%;
  }
}

::ng-deep.modal-content {
  height: 100%;
}

::ng-deep.modal-footer {
  width: 100%;
  position: relative;
  .footer-actions {
    position: fixed;
    bottom: 0;
  }
}

.note-summary {
  height: calc(100vh - 340px);
}

.wrapper-notes {
  .tag-info-badge {
    color: #ffffff;
    background-color: #0f172c;
    border-radius: 5px;
    margin: 0 8px 10px 0;
    font-size: 14px;
  }
}
.summary-field {
  max-height: calc(100vh - 365px);
  overflow-y: auto;
  white-space: pre-line;
}
nb-card {
  padding: 2px 5px;
}
