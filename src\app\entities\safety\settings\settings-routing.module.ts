import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QEAnalyticsGatheringGuard } from '../../qe-analytics/services/qe-analytics-gathering.guard';
import { QE_MENU_MODULE_ENUM } from '../../../@shared/enums/qe-menu.enum';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'general-info'
  },
  {
    path: 'general-info',
    loadChildren: () => import('./general-info/general-info.module').then(m => m.GeneralInfoModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SF_SETTINGS
    }
  },
  {
    path: 'barrier',
    loadChildren: () => import('./JHA-Barrier/barrier/barrier.module').then(m => m.BarrierModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SET_BARRIER,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SF_SETTINGS
    }
  },
  {
    path: 'hazard',
    loadChildren: () => import('./hazard/hazard.module').then(m => m.HazardModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SET_HAZARD,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SF_SETTINGS
    }
  },
  {
    path: 'work-type',
    loadChildren: () => import('./work-type/work-type.module').then(m => m.WorkTypeModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SF_SETTINGS
    }
  },
  {
    path: 'workstep',
    loadChildren: () => import('./workstep/workstep.module').then(m => m.WorkstepModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SF_SETTINGS
    }
  },
  {
    path: 'jha',
    loadChildren: () => import('../jha/jha.module').then(m => m.JhaModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.SF_SET_JHA,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.SF_SETTINGS
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule {}
