<div class="ticketFilter">
  <nb-card>
    <nb-card-header *ngIf="filterHeaderShow">
      <div class="filter-section d-flex align-items-center">
        <span *ngIf="!appliedFilter.length">No filters applied</span>
        <div *ngIf="appliedFilter.length" class="d-sm-flex applied-filter align-items-center">
          <span class="minWidth"> Applied filters: </span>
          <ul class="d-flex flex-wrap ms-sm-1">
            <li class="minWidth d-flex align-items-center m-1" *ngFor="let item of appliedFilter">
              <span>{{ item.text }}:</span><span class="ms-1">{{ item.value }}</span>
              <span class="ms-1 d-flex" (click)="clearSingleFilter(item.text, true)"><nb-icon icon="close-outline"></nb-icon></span>
            </li>
          </ul>
        </div>
        <div class="filter-icon ms-auto d-flex align-items-center">
          <button
            class="linear-mode-button ms-2"
            id="applyFilterButton"
            nbButton
            status="primary"
            size="small"
            type="button"
            nbTooltip="Apply Filter"
            nbTooltipPlacement="top"
            nbTooltipStatus="primary"
            *ngIf="isFilterAllowedWithoutButton() && isFilterOpen"
            (click)="applyFilterClick()"
          >
            <span class="d-lg-inline-block">Filter</span>
          </button>
          <nb-icon
            class="ms-2"
            icon="close-square-outline"
            (click)="clearFilter()"
            nbTooltip="Clear All"
            nbTooltipPlacement="top"
            nbTooltipStatus="primary"
          ></nb-icon>
          <nb-icon
            class="ms-2"
            icon="sync"
            (click)="emitFilterModel()"
            nbTooltip="Refresh"
            nbTooltipPlacement="top"
            nbTooltipStatus="primary"
          ></nb-icon>
          <nb-icon id="sfl-filter-toggler" class="ms-2" icon="funnel" (click)="toggleFilter()"></nb-icon>
        </div>
      </div>
    </nb-card-header>
    <nb-card-body *ngIf="isFilterOpen" class="pe-sm-4">
      <div class="form-control-group">
        <div class="row">
          <!-- Customer Multi select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="
              filterDetails?.filter_item?.CUSTOMER?.show &&
              filterDetails?.filter_item?.CUSTOMER?.multi &&
              filterModel.templateTypeId !== qestFormTemplateTypes.QEST_SUMMARY_REPORT &&
              filterModel.templateTypeId !== qestFormTemplateTypes.QEST_COVER_PAGE &&
              filterModel.templateTypeId !== qestFormTemplateTypes.QEST_TPM_FORM
            "
          >
            <label class="label" for="customer">Customer</label>
            <ng-select
              id="customer-drop-down"
              class="sfl-track-dropdown"
              name="Customer"
              [multiple]="true"
              [items]="customerList"
              (change)="
                onCustomerSelectDeSelect(true);
                addFilter(customerList, filterTitleList.CUSTOMER, 'customerIds');
                clearSingleFilter(filterTitleList.PORTFOLIO, false)
              "
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.customerIds"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              [closeOnSelect]="false"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'customerIds')"
              (close)="filterModelCopy.customerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="customerList && customerList.length">
                <button
                  *ngIf="shouldShowSelectAll('CUSTOMER')"
                  (click)="
                    selectAndDeselectAll(customerList, 'customerIds', true);
                    addFilter(customerList, filterTitleList.CUSTOMER, 'customerIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.CUSTOMER, true); addFilter(customerList, filterTitleList.CUSTOMER, 'customerIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 4 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Customer Single select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.CUSTOMER?.show && !filterDetails?.filter_item?.CUSTOMER?.multi"
          >
            <label class="label" for="customer">Customer</label>
            <ng-select
              id="customer-single-drop-down"
              class="sfl-track-dropdown"
              name="customer"
              [items]="customerList"
              (change)="
                filterModel.customerIds = filterModel?.customerId ? [filterModel?.customerId] : null;
                onCustomerSelectDeSelect(true);
                addFilter($event, filterTitleList.CUSTOMER);
                clearSingleFilter(filterTitleList.PORTFOLIO, false)
              "
              (clear)="clearSingleFilter(filterTitleList.CUSTOMER)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.customerId"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              appendTo="body"
            >
            </ng-select>
          </div>
          <!-- Portfolio Multi select-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.PORTFOLIO?.show">
            <label class="label" for="portfolio">Portfolio</label>
            <ng-select
              id="portfolio-drop-down"
              class="sfl-track-dropdown"
              name="Portfolio"
              [multiple]="true"
              [items]="portfolioList"
              (change)="
                onPortfolioSelectDeSelect(true);
                addFilter(portfolioList, filterTitleList.PORTFOLIO, 'portfolioIds');
                clearSingleFilter(filterTitleList.SITE, false)
              "
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.portfolioIds"
              [closeOnSelect]="false"
              notFoundText="No Portfolio Found"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              placeholder="Select Portfolio"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'portfolioIds')"
              (close)="filterModelCopy.portfolioIds = []"
            >
              <ng-template ng-header-tmp *ngIf="portfolioList && portfolioList.length">
                <button
                  *ngIf="shouldShowSelectAll('PORTFOLIO')"
                  (click)="
                    selectAndDeselectAll(portfolioList, 'portfolioIds', true);
                    addFilter(portfolioList, filterTitleList.PORTFOLIO, 'portfolioIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.PORTFOLIO, true); addFilter(portfolioList, filterTitleList.PORTFOLIO, 'portfolioIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                  >
                    {{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value h-28px" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- SITE Multi select-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.SITE?.show">
            <label class="label" for="input-siteId">Site</label>
            <ng-select
              id="site-drop-down"
              class="sfl-track-dropdown"
              name="Site"
              [multiple]="true"
              [items]="siteList"
              (change)="onFilterChange(true); addFilter(siteList, filterTitleList.SITE, 'siteIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.siteIds"
              [closeOnSelect]="false"
              notFoundText="No Site Found"
              placeholder="Select Site"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'siteIds')"
              (close)="filterModelCopy.siteIds = []"
              [virtualScroll]="true"
            >
              <ng-template ng-header-tmp *ngIf="siteList && siteList.length">
                <button
                  *ngIf="shouldShowSelectAll('SITE')"
                  (click)="selectAndDeselectAll(siteList, 'siteIds', true); addFilter(siteList, filterTitleList.SITE, 'siteIds')"
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.SITE, true); addFilter(siteList, filterTitleList.SITE, 'siteIds')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!--Work Order Search -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.LINKED_TICKET?.show">
            <label *ngIf="linkedTicketSearch" class="label" for="input-siteId">Linked Ticket/ Work Order</label>

            <input
              id="wo-search"
              class="form-control search-textbox deviceSearch sfl-track-input"
              placeholder="{{ filterDetails?.placeholder }}"
              type="text"
              name="search"
              autocomplete="off"
              [(ngModel)]="filterModel.search"
              (ngModelChange)="onSearchChanged(); addFilter({ name: filterModel.search }, filterTitleList.WORK_ORDER)"
            />
          </div>

          <!-- PRIORITY  Multi select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.PRIORITY?.show && this.filterDetails.filter_item.PRIORITY?.multi"
          >
            <label class="label" for="input-state">Priority</label>
            <ng-select
              id="priority-drop-down"
              class="sfl-track-dropdown"
              name="Priority"
              [items]="ticketPriority"
              (change)="onFilterChange(true); addFilter(ticketPriority, filterTitleList.PRIORITY, 'priorityIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.priorityIds"
              notFoundText="No Priority Found"
              placeholder="Select Priority"
              [multiple]="true"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'priorityIds')"
              (close)="filterModelCopy.priorityIds = []"
            >
              <ng-template ng-header-tmp *ngIf="ticketPriority && ticketPriority.length">
                <button
                  (click)="
                    selectAndDeselectAll(ticketPriority, 'priorityIds', true);
                    addFilter(ticketPriority, filterTitleList.PRIORITY, 'priorityIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.PRIORITY, true); addFilter(ticketPriority, filterTitleList.PRIORITY, 'priorityIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- PRIORITY Single select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.PRIORITY?.show && !this.filterDetails.filter_item.PRIORITY?.multi"
          >
            <label class="label" for="input-state">Priority</label>
            <ng-select
              id="priority-single-drop-down"
              class="sfl-track-dropdown"
              name="Priority"
              [items]="ticketPriority"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.PRIORITY)"
              (clear)="clearSingleFilter(filterTitleList.PRIORITY)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.priorityId"
              notFoundText="No Priority Found"
              placeholder="Select Priority"
              appendTo="body"
            >
            </ng-select>
          </div>
          <!-- STATE -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.STATE?.show">
            <label class="label" for="input-state">State</label>
            <ng-select
              id="state-drop-down"
              class="sfl-track-dropdown"
              name="State"
              [items]="stateList"
              (change)="onFilterChange(true); addFilter(stateList, filterTitleList.STATE, 'states', 'name')"
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.states"
              notFoundText="No State Found"
              placeholder="Select State"
              [multiple]="true"
              [closeOnSelect]="false"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'states', 'name')"
              (close)="filterModelCopy.states = []"
            >
              <ng-template ng-header-tmp *ngIf="stateList && stateList.length">
                <button
                  (click)="
                    selectAndDeselectAll(stateList, 'states', true, 'name'); addFilter(stateList, filterTitleList.STATE, 'states', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.STATE, true); addFilter(stateList, filterTitleList.STATE, 'states', 'name')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- Minimum Affected kW -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.MINIMUM_AFFECTED_KW?.show"
          >
            <label class="label" for="input-state">Minimum Affected kW</label>
            <input
              id="min-affected-kw-input"
              class="form-control search-textbox sfl-track-input"
              placeholder="Minimum Affected kW"
              type="text"
              name="affectedkw"
              autocomplete="off"
              sflValidators
              [(ngModel)]="filterModel.affectedkw"
              (ngModelChange)="affectedkwChanged(); addFilter({ name: filterModel.affectedkw }, filterTitleList?.MINIMUM_AFFECTED_KW)"
            />
          </div>
          <!-- STATUS Multi select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.STATUS?.show && filterDetails.filter_item.STATUS.multi"
          >
            <label class="label" for="input-state">Status</label>
            <ng-select
              id="status-drop-down"
              class="sfl-track-dropdown"
              name="Status"
              [items]="ticketStatus"
              (change)="onFilterChange(true); addFilter(ticketStatus, filterTitleList.STATUS, 'statusIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.statusIds"
              notFoundText="No Status Found"
              placeholder="Select Status"
              [multiple]="true"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'statusIds')"
              (close)="filterModelCopy.customerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="ticketStatus && ticketStatus.length">
                <button
                  (click)="
                    selectAndDeselectAll(ticketStatus, 'statusIds', true); addFilter(ticketStatus, filterTitleList.STATUS, 'statusIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.STATUS, true); addFilter(ticketStatus, filterTitleList.STATUS, 'statusIds')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- STATUS Single select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.STATUS?.show && !filterDetails.filter_item.STATUS.multi"
          >
            <label class="label" for="input-state">Ticket Status</label>
            <ng-select
              id="status-single-drop-down"
              class="sfl-track-dropdown"
              name="Status"
              [items]="ticketStatus"
              (change)="refreshListInParent(); addFilter($event, filterTitleList.STATUS)"
              (clear)="clearSingleFilter(filterTitleList.STATUS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.ticketStatus"
              notFoundText="No Status Found"
              placeholder="Select Status"
              appendTo="body"
            >
            </ng-select>
          </div>
          <!-- QE Tech Multi select-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.QE_TECH?.show">
            <label class="label" for="input-siteId">QE Tech</label>
            <ng-select
              id="qe-tech-drop-down"
              class="sfl-track-dropdown"
              name="Site"
              [multiple]="true"
              [items]="fieldTechList"
              (change)="onFilterChange(true); addFilter(fieldTechList, filterTitleList.QE_TECH, 'userIds')"
              (clear)="clearSingleFilter(filterTitleList.QE_TECH)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.userIds"
              [closeOnSelect]="false"
              notFoundText="No QE Tech Found"
              placeholder="Select QE Tech"
              appendTo="body"
              (search)="onFilterSearch($event, 'userIds')"
              (close)="filterModelCopy.userIds = []"
            >
              <ng-template ng-header-tmp *ngIf="fieldTechList && fieldTechList.length">
                <button
                  (click)="
                    selectAndDeselectAll(fieldTechList, 'userIds', true); addFilter(fieldTechList, filterTitleList.QE_TECH, 'userIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.QE_TECH, true); addFilter(fieldTechList, filterTitleList.QE_TECH, 'userIds')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Region Multi select-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.REGION?.show">
            <label class="label" for="input-siteId">Region</label>
            <ng-select
              id="region-drop-down"
              class="sfl-track-dropdown"
              name="Region"
              [multiple]="true"
              [items]="regionList"
              (change)="onFilterChange(true); addFilter(regionList, filterTitleList.REGION, 'regionIds')"
              (clear)="clearSingleFilter(filterTitleList.REGION)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.regionIds"
              [closeOnSelect]="false"
              notFoundText="No Region Found"
              placeholder="Select Region"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'regionIds')"
              (close)="filterModelCopy.regionIds = []"
            >
              <ng-template ng-header-tmp *ngIf="regionList && regionList.length">
                <button
                  (click)="selectAndDeselectAll(regionList, 'regionIds', true); addFilter(regionList, filterTitleList.REGION, 'regionIds')"
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.REGION, true); addFilter(regionList, filterTitleList.REGION, 'regionIds')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Subregion Multi select-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.SUB_REGION?.show">
            <label class="label" for="input-siteId">Subregion</label>
            <ng-select
              id="region-drop-down"
              class="sfl-track-dropdown"
              name="Subregion"
              [multiple]="true"
              [items]="subRegionList"
              (change)="onFilterChange(true); addFilter(subRegionList, filterTitleList.SUB_REGION, 'subregionIds')"
              (clear)="clearSingleFilter(filterTitleList.SUB_REGION)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.subregionIds"
              [closeOnSelect]="false"
              notFoundText="No Subregion Found"
              placeholder="Select Subregion"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'subregionIds')"
              (close)="filterModelCopy.subregionIds = []"
            >
              <ng-template ng-header-tmp *ngIf="subRegionList && subRegionList.length">
                <button
                  (click)="
                    selectAndDeselectAll(subRegionList, 'subregionIds', true);
                    addFilter(subRegionList, filterTitleList.SUB_REGION, 'subregionIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SUB_REGION, true);
                    addFilter(subRegionList, filterTitleList.SUB_REGION, 'subregionIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- User Type Multi select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.USER_TYPE?.show && filterDetails.filter_item.USER_TYPE.multi"
          >
            <label class="label" for="input-user-type">User Type</label>
            <ng-select
              id="user-type-drop-down"
              class="sfl-track-dropdown"
              name="userTypeIds"
              [items]="userTypeList"
              (change)="
                onFilterChange(true);
                addFilter(userTypeList, filterTitleList.USER_TYPE, 'userTypeIds');
                setQEUserList();
                filterModel.userIds = []
              "
              (clear)="clearSingleFilter(filterTitleList.USER_TYPE, true)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.userTypeIds"
              notFoundText="No User Type Found"
              placeholder="Select User Type"
              [multiple]="true"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'userTypeIds')"
            >
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- User Type single select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.USER_TYPE?.show && !filterDetails.filter_item.USER_TYPE.multi"
          >
            <label class="label" for="input-user-type">User Type</label>
            <ng-select
              id="user-type-drop-down"
              class="sfl-track-dropdown"
              name="userTypeId"
              [items]="userTypeList"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.USER_TYPE); setQEUserList(); filterModel.userIds = []"
              (clear)="clearSingleFilter(filterTitleList.USER_TYPE, true)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.userTypeId"
              notFoundText="No User Type Found"
              placeholder="Select User Type"
              appendTo="body"
            >
            </ng-select>
          </div>

          <!--User-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.USER?.show">
            <label class="label" for="input-siteId">User</label>
            <ng-select
              id="users-drop-down"
              class="sfl-track-dropdown"
              name="Site"
              [multiple]="true"
              [items]="qeUserList"
              (change)="onFilterChange(true); addFilter(qeUserList, filterTitleList.USER, 'userIds')"
              (clear)="clearSingleFilter(filterTitleList.USER)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.userIds"
              [closeOnSelect]="false"
              notFoundText="No User Found"
              placeholder="Select User"
              appendTo="body"
              (search)="onFilterSearch($event, 'userIds')"
              (open)="setQEUserList()"
              (close)="filterModelCopy.userIds = []; setQEUserList()"
            >
              <ng-template ng-header-tmp *ngIf="qeUserList && qeUserList.length">
                <button
                  (click)="selectAndDeselectAll(qeUserList, 'userIds', true); addFilter(qeUserList, filterTitleList.USER, 'userIds')"
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.USER, true); addFilter(qeUserList, filterTitleList.USER, 'userIds')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- OPENED -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.OPEN_DATE?.show && billingPageViewType === 'billingView'"
          >
            <label class="label" for="input-date">Opened</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="openDate"
              [(ngModel)]="filterModel.openDate"
              name="openDate"
              placeholder="Select"
              id="input-openDate"
              readonly
              autocomplete="off"
              (ngModelChange)="fromToDateChanged($event); addFilter({ name: filterModel.openDate }, 'Opened')"
              (clear)="clearSingleFilter(filterTitleList.OPEN_DATE)"
            />
            <nb-rangepicker #openDate></nb-rangepicker>
          </div>

          <!--DATE-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.DATE?.show">
            <label class="label" for="input-date">Date</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="date"
              [(ngModel)]="filterModel.date"
              name="activityStartDate"
              placeholder="Select"
              id="input-activityStartDate"
              readonly
              autocomplete="off"
              (ngModelChange)="fromToDateChanged($event); addFilter({ name: filterModel.date }, 'Date')"
              (clear)="clearSingleFilter(filterTitleList.DATE)"
            />
            <nb-rangepicker #date></nb-rangepicker>
          </div>

          <!-- only check-in -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.CHECK_IN_ONLY?.show">
            <label class="label d-flex" for="customer"> Check-In Only </label><br />
            <nb-toggle
              id="checkin-toggle"
              name="isLink"
              [(checked)]="filterModel.checkInOnly"
              (checkedChange)="checkInOnly($event)"
              class="marginTop sfl-track-toggle"
              status="primary"
            ></nb-toggle>
          </div>
          <!-- CLOSE -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.CLOSE_DATE?.show && billingPageViewType === 'billingView'"
          >
            <label class="label" for="input-state">Closed</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="closeDate"
              [(ngModel)]="filterModel.closeDate"
              name="closeDate"
              placeholder="Select"
              id="input-closeDate"
              readonly
              autocomplete="off"
              (ngModelChange)="fromToDateChanged($event); addFilter({ name: filterModel.closeDate }, 'Closed')"
            />
            <nb-rangepicker #closeDate></nb-rangepicker>
          </div>

          <!-- ACTIVITY RANGE -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.ACTIVITY_RANGE?.show">
            <label class="label" for="input-date">Activity Range</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="activityRange"
              [(ngModel)]="filterModel.activityRange"
              name="activityRange"
              placeholder="Select"
              id="input-openDate"
              readonly
              autocomplete="off"
              (ngModelChange)="fromToDateChanged($event); addFilter({ name: filterModel.activityRange }, 'Activity Range')"
              (clear)="clearSingleFilter(filterTitleList.ACTIVITY_RANGE)"
            />
            <nb-rangepicker #activityRange></nb-rangepicker>
          </div>
          <!-- rmaComplete -->
          <div class="col-12 col-sm-2 pr-0 mb-2" *ngIf="filterDetails?.filter_item?.RMA_COMPLETE?.show">
            <label class="label" for="input-date">Complete</label>
            <ng-select
              id="rma-complete-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              (change)="onFilterChange(true); addFilter({ name: $event }, filterTitleList.RMA_COMPLETE)"
              (clear)="clearSingleFilter(filterTitleList.RMA_COMPLETE)"
              [(ngModel)]="filterModel.rmaComplete"
              placeholder="Select Complete"
              appendTo="body"
            >
              <ng-option value="Yes">Yes</ng-option>
              <ng-option value="No">No</ng-option>
            </ng-select>
          </div>
          <!-- rmaTracking -->
          <div class="col-12 col-sm-2 pr-0 mb-2" *ngIf="filterDetails?.filter_item?.RMA_TRACKING?.show">
            <label class="label" for="input-date">Tracking</label>
            <ng-select
              id="rma-tracking-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              (change)="onFilterChange(true); addFilter({ name: $event }, filterTitleList.RMA_TRACKING)"
              (clear)="clearSingleFilter(filterTitleList.RMA_TRACKING)"
              [(ngModel)]="filterModel.rmaTracking"
              placeholder="Select Tracking"
              appendTo="body"
            >
              <ng-option value="Yes">Yes</ng-option>
              <ng-option value="No">No</ng-option>
            </ng-select>
          </div>
          <!-- rmaReturnRequired -->
          <div class="col-12 col-sm-2 pr-0 mb-2" *ngIf="filterDetails?.filter_item?.RMA_RETURN_REQUIRED?.show">
            <label class="label" for="input-date">Return Required</label>
            <ng-select
              id="rma-return-require-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              (change)="onFilterChange(true); addFilter({ name: $event }, filterTitleList.RMA_RETURN_REQUIRED)"
              (clear)="clearSingleFilter(filterTitleList.RMA_RETURN_REQUIRED)"
              [(ngModel)]="filterModel.rmaReturnRequired"
              placeholder="Select Tracking"
              appendTo="body"
            >
              <ng-option value="Yes">Yes</ng-option>
              <ng-option value="No">No</ng-option>
            </ng-select>
          </div>
          <!-- Resolved -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.RESOLVED?.show">
            <label class="label" for="input-date">Resolved</label>
            <ng-select
              id="resolved-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              (change)="onFilterChange(true); addFilter({ name: $event }, filterTitleList.RESOLVED)"
              (clear)="clearSingleFilter(filterTitleList.RESOLVED)"
              [(ngModel)]="filterModel.isResolve"
              placeholder="Select Resolved"
              appendTo="body"
            >
              <ng-option value="Yes">Yes</ng-option>
              <ng-option value="No">No</ng-option>
            </ng-select>
          </div>

          <!-- EXCLUSION TO -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.EXCLUSION_TO?.show">
            <label class="label" for="input-date">Exclusion To</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="exclusionTo"
              [(ngModel)]="filterModel.exclusionTo"
              name="exclusionTo"
              placeholder="Select"
              id="input-exclusionTo"
              readonly
              autocomplete="off"
              (ngModelChange)="fromToDateChanged($event); addFilter({ name: filterModel.exclusionTo }, 'Exclusion To')"
              (clear)="clearSingleFilter(filterTitleList.EXCLUSION_TO)"
            />
            <nb-rangepicker #exclusionTo></nb-rangepicker>
          </div>
          <!-- EXCLUSION FROM -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.EXCLUSION_FROM?.show">
            <label class="label" for="input-state">Exclusion From</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="exclusionFrom"
              [(ngModel)]="filterModel.exclusionFrom"
              name="exclusionFrom"
              placeholder="Select"
              id="input-exclusionFrom"
              readonly
              autocomplete="off"
              (ngModelChange)="fromToDateChanged($event); addFilter({ name: filterModel.exclusionFrom }, 'Exclusion From')"
              (clear)="clearSingleFilter(filterTitleList.EXCLUSION_FROM)"
            />
            <nb-rangepicker #exclusionFrom></nb-rangepicker>
          </div>

          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.ASSESSMENT_TYPE?.show">
            <label class="label" for="input-workorderId">Assessment Type</label>
            <ng-select
              id="assessment-type-drop-down"
              class="sfl-track-dropdown"
              name="Report Type"
              [multiple]="true"
              [items]="reportTypeData"
              (change)="
                onFilterChange(true);
                addFilter(reportTypeData, filterTitleList.ASSESSMENT_TYPE, 'reportTypeIds');
                addRemoveIsExclusiveFilter()
              "
              (clear)="clearSingleFilter(filterTitleList.ASSESSMENT_TYPE); clearSingleFilter(filterTitleList.IS_INCLUSIVE_SEARCH)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.reportTypeIds"
              notFoundText="No Assessment Type Found"
              placeholder="Select Assessment Type"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'reportTypeIds')"
              (close)="filterModelCopy.reportTypeIds = []"
            >
              <ng-template ng-header-tmp *ngIf="reportTypeData && reportTypeData.length">
                <div [ngClass]="{ 'd-flex justify-content-between': filterDetails.page_name === 'dashboardPage' }">
                  <button
                    (click)="
                      selectAndDeselectAll(reportTypeData, 'reportTypeIds', true);
                      addFilter(reportTypeData, filterTitleList.ASSESSMENT_TYPE, 'reportTypeIds');
                      addRemoveIsExclusiveFilter()
                    "
                    class="btn btn-sm btn-primary"
                  >
                    Select all
                  </button>
                  <button
                    (click)="
                      clearSingleFilter(filterTitleList.ASSESSMENT_TYPE, true);
                      addFilter(reportTypeData, filterTitleList.ASSESSMENT_TYPE, 'reportTypeIds')
                    "
                    class="btn btn-sm btn-primary ms-1"
                  >
                    Unselect all
                  </button>
                </div>
                <div class="exclusive-toggle" *ngIf="filterDetails.page_name === 'dashboardPage'">
                  <label class="label me-2 mb-0 d-flex mt-1" for="customer">
                    {{ filterModel.isInclusiveSearch ? 'Inclusive' : 'Exclusive' }}
                  </label>
                  <nb-toggle
                    id="linked-feild-toggle"
                    class="sfl-track-toggle"
                    name="isLink"
                    [(checked)]="filterModel.isInclusiveSearch"
                    (checkedChange)="isExclusiveSearch()"
                    status="primary"
                  ></nb-toggle>
                </div>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.REPORT_STATUS?.show">
            <label class="label" for="input-workorderId">Report Status</label>
            <ng-select
              id="report-status-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              [items]="reportstatus"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.REPORT_STATUS)"
              (clear)="clearSingleFilter(filterTitleList.REPORT_STATUS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.reportStatus"
              notFoundText="No Report Status Found"
              placeholder="Select Report Status"
              appendTo="body"
            >
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.WORKORDER_STATUS?.show">
            <label class="label" for="input-woStatus">Work Order Status</label>
            <ng-select
              id="wo-status-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              [items]="woDropstatusList"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.WORKORDER_STATUS)"
              (clear)="clearSingleFilter(filterTitleList.WORKORDER_STATUS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.woStatus"
              notFoundText="No Work Order Status Found"
              placeholder="Select Work Order Status"
              appendTo="body"
            >
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.FREQUENCY_TYPE?.show">
            <label class="label" for="input-woStatus">Frequency</label>
            <ng-select
              id="frequency-drop-down"
              class="sfl-track-dropdown"
              [items]="frequencytype"
              [multiple]="true"
              bindLabel="name"
              groupBy="type"
              [selectableGroup]="true"
              [selectableGroupAsModel]="false"
              [closeOnSelect]="false"
              bindValue="name"
              [(ngModel)]="filterModel.frequencies"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.FREQUENCY_TYPE)"
              (clear)="clearSingleFilter(filterTitleList.FREQUENCY_TYPE)"
              placeholder="Select Frequency"
              appendTo="body"
            >
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.type }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name | uppercase }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- MULTI SELECT YEAR-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0"
            *ngIf="filterDetails?.filter_item?.START_YEAR?.show && this.filterDetails.filter_item.START_YEAR?.multi"
          >
            <label class="label" for="input-workorderId">Year</label>
            <ng-select
              class="sfl-track-dropdown"
              id="year"
              name="year"
              [multiple]="true"
              [(ngModel)]="filterModel.years"
              [closeOnSelect]="false"
              [items]="years"
              bindLabel="name"
              bindValue="id"
              notFoundText="No Year Found"
              placeholder="Select Year"
              (change)="onFilterChange(true); addFilter(years, filterTitleList.START_YEAR, 'years')"
              [clearable]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'years')"
              (close)="filterModelCopy.years = []"
            >
              <ng-template ng-header-tmp *ngIf="years && years.length">
                <button
                  (click)="selectAndDeselectAll(years, 'years', true); addFilter(years, filterTitleList.START_YEAR, 'years')"
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.START_YEAR, true); addFilter(years, filterTitleList.START_YEAR, 'years')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" *ngIf="items.length > 1" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.ARRAY_TYPE?.show">
            <label class="label" for="input-workorderId">Array Type</label>
            <ng-select
              id="array-type-drop-down"
              class="sfl-track-dropdown"
              name="siteTypes"
              [multiple]="true"
              [items]="arrayTypeList"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.arrayTypeIds"
              (change)="onFilterChange(true); addFilter(arrayTypeList, filterTitleList.ARRAY_TYPE, 'arrayTypeIds')"
              [closeOnSelect]="false"
              notFoundText="No Array Type Found"
              placeholder="Select Array Type"
              [disabled]="disabled"
              appendTo="body"
              (search)="onFilterSearch($event, 'arrayTypeIds')"
              (close)="filterModelCopy.arrayTypeIds = []"
            >
              <ng-template ng-header-tmp *ngIf="arrayTypeList && arrayTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(arrayTypeList, 'arrayTypeIds', true);
                    addFilter(arrayTypeList, filterTitleList.ARRAY_TYPE, 'arrayTypeIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.ARRAY_TYPE, true);
                    addFilter(arrayTypeList, filterTitleList.ARRAY_TYPE, 'arrayTypeIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
                {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.SEARCH_BOX?.show">
            <label *ngIf="!siteSearch" class="label" for="input-search">Search</label>
            <label *ngIf="siteSearch" class="label" for="input-search">Site Search</label>
            <input
              id="site-search-input"
              class="form-control search-textbox deviceSearch sfl-track-input"
              placeholder="{{ filterDetails?.placeholder }}"
              type="text"
              name="search"
              autocomplete="off"
              [(ngModel)]="filterModel.search"
              (ngModelChange)="onSearchChanged(); addFilter({ name: filterModel.search }, filterTitleList.SEARCH_BOX)"
            />
          </div>
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0"
            *ngIf="filterDetails?.filter_item?.DEVICE_TYPE?.show && filterDetails.filter_item.DEVICE_TYPE.multi"
          >
            <label class="label" for="device">Device Type</label>
            <ng-select
              id="device-type-drop-down"
              class="sfl-track-dropdown"
              name="device"
              [items]="deviceTypeList"
              [multiple]="true"
              (change)="onFilterChange(true); addFilter(deviceTypeList, filterTitleList.DEVICE_TYPE, 'deviceTypeIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.deviceTypeIds"
              notFoundText="No Device Found"
              placeholder="Select Device"
              class="search-textbox"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'deviceTypeIds')"
              (close)="filterModelCopy.deviceTypeIds = []"
            >
              <ng-template ng-header-tmp *ngIf="deviceTypeList && deviceTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(deviceTypeList, 'deviceTypeIds', true);
                    addFilter(deviceTypeList, filterTitleList.DEVICE_TYPE, 'deviceTypeIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.DEVICE_TYPE, true);
                    addFilter(deviceTypeList, filterTitleList.DEVICE_TYPE, 'deviceTypeIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!--DEVICE-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 mb-2 pe-sm-0" *ngIf="filterDetails?.filter_item?.DEVICE?.show">
            <label class="label" for="site">Device</label>
            <input
              id="device-input"
              class="form-control search-textbox sfl-track-input"
              placeholder="Search Device"
              type="text"
              name="search"
              autocomplete="off"
              [(ngModel)]="filterModel.DeviceName"
              (ngModelChange)="deviceSearchChanged(); addFilter({ name: filterModel.DeviceName }, 'DeviceName')"
            />
          </div>

          <!--MFG-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.MFG?.show">
            <label class="label" for="input-siteId">Manufacturer</label>
            <ng-select
              id="mfg-drop-down"
              class="sfl-track-dropdown"
              name="automation site"
              [multiple]="true"
              [items]="mfgList"
              (change)="onFilterChange(true); addFilter(mfgList, filterTitleList.MFG, 'mfgs', 'name'); updateModelListValues()"
              (clear)="clearSingleFilter(filterTitleList.MFG)"
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.mfgs"
              [closeOnSelect]="false"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              notFoundText="No Mfg Found"
              placeholder="Select Manufacturer"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'mfgs', 'name')"
              (close)="filterModelCopy.mfgs = []"
            >
              <ng-template ng-header-tmp *ngIf="mfgList && mfgList.length">
                <button
                  (click)="
                    selectAndDeselectAll(mfgList, 'mfgs', true, 'name');
                    addFilter(mfgList, filterTitleList.MFG, 'mfgs', 'name');
                    updateModelListValues()
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="clearSingleFilter(filterTitleList.MFG, true); addFilter(mfgList, filterTitleList.MFG, 'mfgs', 'name')"
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!--MODEL-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.MODEL?.show">
            <label class="label" for="input-siteId">Model</label>
            <ng-select
              id="model-drop-down"
              class="sfl-track-dropdown"
              name="automation site"
              [multiple]="true"
              [items]="modelList"
              (change)="onFilterChange(true); addFilter(modelList, filterTitleList.MODEL, 'deviceModels', 'name')"
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.deviceModels"
              [closeOnSelect]="false"
              notFoundText="No Model Found"
              placeholder="Select Model"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
              (search)="onFilterSearch($event, 'deviceModels', 'name')"
              (close)="filterModelCopy.deviceModels = []"
              [virtualScroll]="true"
            >
              <ng-template ng-header-tmp *ngIf="modelList && modelList.length">
                <button
                  (click)="
                    selectAndDeselectAll(modelList, 'deviceModels', true, 'name');
                    addFilter(modelList, filterTitleList.MODEL, 'deviceModels', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.MODEL, true); addFilter(modelList, filterTitleList.MODEL, 'deviceModels', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name + ' [' + item.mfgName + ']' }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mt-2" *ngIf="filterDetails?.filter_item?.SHOW_DELETED?.show">
            <nb-checkbox
              id="show-deleted-checkbox"
              class="sfl-track-checkbox"
              *ngIf="user[0] !== 'customer'"
              name="isDelete"
              [checked]="filterModel.isDelete"
              (change)="restoreDeletedReport($event)"
              ><span class="fw-bold">Show Deleted</span></nb-checkbox
            >
          </div>

          <!-- Activity start -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.ACTIVITY_START?.show">
            <label class="label" for="input-date">Activity Range Start</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="activityStartDate"
              [(ngModel)]="filterModel.activityRangeStart"
              name="activityStartDate"
              placeholder="Select"
              id="input-activityStartDate"
              readonly
              autocomplete="off"
              (ngModelChange)="dateChanged($event); addFilter({ name: filterModel.activityRangeStart }, filterTitleList.ACTIVITY_START)"
              (clear)="clearSingleFilter(filterTitleList.ACTIVITY_START)"
            />
            <nb-datepicker #activityStartDate></nb-datepicker>
          </div>

          <!-- Activity end -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.ACTIVITY_END?.show">
            <label class="label" for="input-date">Activity Range End</label>
            <input
              class="form-control search-textbox sfl-track-input"
              [nbDatepicker]="activityEndDate"
              [(ngModel)]="filterModel.activityRangeEnd"
              name="activityEndDate"
              placeholder="Select"
              id="input-activityEndDate"
              readonly
              autocomplete="off"
              (ngModelChange)="dateChanged($event); addFilter({ name: filterModel.activityRangeEnd }, filterTitleList.ACTIVITY_END)"
              (clear)="clearSingleFilter(filterTitleList.ACTIVITY_END)"
            />
            <nb-datepicker #activityEndDate></nb-datepicker>
          </div>

          <!-- Truck Roll -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.TRUCK_ROLL?.show">
            <label class="label" for="input-date">Truck Roll</label>
            <ng-select
              id="truck-roll-drop-down"
              class="sfl-track-dropdown"
              name="truckRollType"
              [multiple]="true"
              [items]="truckRollTypeList"
              (change)="
                truckRollChange($event); onFilterChange(true); addFilter(truckRollTypeList, filterTitleList.TRUCK_ROLL, 'truckRoll')
              "
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.truckRoll"
              [closeOnSelect]="false"
              notFoundText="No Truck Roll Found"
              placeholder="Select Truck Roll"
              appendTo="body"
              [virtualScroll]="true"
            >
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" [disabled]="item.disabled" />
                {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SHOW_STATUS?.show && user[0] !== 'customer'"
          >
            <label class="label" for="input-date">Status</label>
            <ng-select
              id="status-truck-roll-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              (change)="onFilterChange(true); addFilter({ name: $event }, filterTitleList.SHOW_STATUS)"
              (clear)="clearSingleFilter(filterTitleList.SHOW_STATUS)"
              [(ngModel)]="filterModel.isActive"
              placeholder="Select Status"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
            >
              <ng-option value="Active">Active</ng-option>
              <ng-option value="InActive">Inactive</ng-option>
            </ng-select>
          </div>

          <!--Site Audit Report Customer-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="
              filterDetails?.filter_item?.SITE_AUDIT_REPORT_CUSTOMER?.show && filterDetails?.filter_item?.SITE_AUDIT_REPORT_CUSTOMER?.multi
            "
          >
            <label class="label" for="customer">Customer</label>
            <ng-select
              id="audit-customer-drop-down"
              class="sfl-track-dropdown"
              name="Customer"
              [multiple]="true"
              [items]="siteAuditReportCustomerList"
              (change)="
                onFilterChange(true);
                addFilter(siteAuditReportCustomerList, filterTitleList.SITE_AUDIT_REPORT_CUSTOMER, 'customerNames', 'name')
              "
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.customerNames"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'customerNames', 'name')"
              (close)="filterModelCopy.customerNames = []"
            >
              <ng-template ng-header-tmp *ngIf="siteAuditReportCustomerList && siteAuditReportCustomerList.length">
                <button
                  (click)="
                    selectAndDeselectAll(siteAuditReportCustomerList, 'customerNames', true, 'name');
                    addFilter(siteAuditReportCustomerList, filterTitleList.SITE_AUDIT_REPORT_CUSTOMER, 'customerNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_REPORT_CUSTOMER, true);
                    addFilter(siteAuditReportCustomerList, filterTitleList.SITE_AUDIT_REPORT_CUSTOMER, 'customerNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Portfolio Multi select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SITE_AUDIT_REPORT_PORTFOLIO?.show"
          >
            <label class="label" for="portfolio">Portfolio</label>
            <ng-select
              id="site-audit-portfolio-drop-down"
              class="sfl-track-dropdown"
              name="SiteAuditPortfolio"
              [multiple]="true"
              [items]="siteAuditReportPortfolioList"
              (change)="
                onFilterChange(true);
                addFilter(siteAuditReportPortfolioList, filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO, 'portfolioNames', 'name')
              "
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.portfolioNames"
              [closeOnSelect]="false"
              notFoundText="No Portfolio Found"
              placeholder="Select Portfolio"
              appendTo="body"
              (search)="onFilterSearch($event, 'portfolioNames', 'name')"
              (close)="filterModelCopy.portfolioNames = []"
            >
              <ng-template ng-header-tmp *ngIf="siteAuditReportPortfolioList && siteAuditReportPortfolioList.length">
                <button
                  (click)="
                    selectAndDeselectAll(siteAuditReportPortfolioList, 'portfolioNames', true, 'name');
                    addFilter(siteAuditReportPortfolioList, filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO, 'portfolioNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO, true);
                    addFilter(siteAuditReportPortfolioList, filterTitleList.SITE_AUDIT_REPORT_PORTFOLIO, 'portfolioNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- SITE Multi select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SITE_AUDIT_REPORT_SITE?.show"
          >
            <label class="label" for="input-siteId">Site</label>
            <ng-select
              id="audit-sites-drop-down"
              class="sfl-track-dropdown"
              name="Site"
              [multiple]="true"
              [items]="siteAuditReportSiteList"
              (change)="
                onFilterChange(true); addFilter(siteAuditReportSiteList, filterTitleList.SITE_AUDIT_REPORT_SITE, 'siteNames', 'name')
              "
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.siteNames"
              [closeOnSelect]="false"
              notFoundText="No Site Found"
              placeholder="Select Site"
              appendTo="body"
              (search)="onFilterSearch($event, 'siteNames', 'name')"
              (close)="filterModelCopy.siteNames = []"
            >
              <ng-template ng-header-tmp *ngIf="siteAuditReportSiteList && siteAuditReportSiteList.length">
                <button
                  (click)="
                    selectAndDeselectAll(siteAuditReportSiteList, 'siteNames', true, 'name');
                    addFilter(siteAuditReportSiteList, filterTitleList.SITE_AUDIT_REPORT_SITE, 'siteNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_REPORT_SITE, true);
                    addFilter(siteAuditReportSiteList, filterTitleList.SITE_AUDIT_REPORT_SITE, 'siteNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Site Audit JHA  customer dropdown start-->
          <!-- <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.SHOW_NERC?.show">
            <label class="label" for="input-date">NERC</label>
            <ng-select
              id="jha-nerc-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              (change)="onFilterChange(true); addFilter({ name: $event }, filterTitleList.SHOW_NERC)"
              (clear)="clearSingleFilter(filterTitleList.SHOW_NERC)"
              [(ngModel)]="filterModel.isNERC"
              placeholder="Select NERC"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              [appendTo]="filterAppendPosition"
            >
              <ng-option value="All">All</ng-option>
              <ng-option value="Yes">Yes</ng-option>
              <ng-option value="No">No</ng-option>
            </ng-select>
          </div> -->

          <!-- NERC SITE FILTER -->

          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SHOW_NERC_SITE_TYPE?.show"
          >
            <label class="label" for="input-workorderId">NERC</label>
            <ng-select
              id="jha-nerc-drop-down"
              class="sfl-track-dropdown"
              name="ReportType"
              [items]="nercSiteTypeDropDown"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.SHOW_NERC_SITE_TYPE)"
              (clear)="clearSingleFilter(filterTitleList.SHOW_NERC_SITE_TYPE)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.nercSiteTypeId"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              notFoundText="No NERC Found"
              placeholder="Select NERC"
              [appendTo]="filterAppendPosition"
            >
            </ng-select>
          </div>

          <!-- Site Audit customer dropdown start-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SITE_AUDIT_CUSTOMER?.show && filterDetails?.filter_item?.SITE_AUDIT_CUSTOMER?.multi"
          >
            <label class="label" for="customer">Customer</label>
            <ng-select
              id="site-audit-customer-drop-down"
              class="sfl-track-dropdown"
              name="Customer"
              [multiple]="true"
              [items]="siteAuditCustomerList"
              (change)="
                onFilterChange(true); addFilter(siteAuditCustomerList, filterTitleList.SITE_AUDIT_CUSTOMER, 'customerNames', 'name')
              "
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.customerNames"
              notFoundText="No Customer Found"
              placeholder="Select Customer"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'customerNames', 'name')"
              (close)="filterModelCopy.customerNames = []"
            >
              <ng-template ng-header-tmp *ngIf="siteAuditCustomerList && siteAuditCustomerList.length">
                <button
                  (click)="
                    selectAndDeselectAll(siteAuditCustomerList, 'customerNames', true, 'name');
                    addFilter(siteAuditCustomerList, filterTitleList.SITE_AUDIT_CUSTOMER, 'customerNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_CUSTOMER, true);
                    addFilter(siteAuditCustomerList, filterTitleList.SITE_AUDIT_CUSTOMER, 'customerNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Portfolio Multi select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SITE_AUDIT_PORTFOLIO?.show"
          >
            <label class="label" for="portfolio">Portfolio</label>
            <ng-select
              id="audit-portfolio-multi-drop-down"
              class="sfl-track-dropdown"
              name="SiteAuditPortfolio"
              [multiple]="true"
              [items]="siteAuditPortfolioList"
              (change)="
                onFilterChange(true); addFilter(siteAuditPortfolioList, filterTitleList.SITE_AUDIT_PORTFOLIO, 'portfolioNames', 'name')
              "
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.portfolioNames"
              [closeOnSelect]="false"
              notFoundText="No Portfolio Found"
              placeholder="Select Portfolio"
              appendTo="body"
              (search)="onFilterSearch($event, 'portfolioNames', 'name')"
              (close)="filterModelCopy.portfolioNames = []"
            >
              <ng-template ng-header-tmp *ngIf="siteAuditPortfolioList && siteAuditPortfolioList.length">
                <button
                  (click)="
                    selectAndDeselectAll(siteAuditPortfolioList, 'portfolioNames', true, 'name');
                    addFilter(siteAuditPortfolioList, filterTitleList.SITE_AUDIT_PORTFOLIO, 'portfolioNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_PORTFOLIO, true);
                    addFilter(siteAuditPortfolioList, filterTitleList.SITE_AUDIT_PORTFOLIO, 'portfolioNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- SITE Multi select-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.SITE_AUDIT_SITE?.show">
            <label class="label" for="input-siteId">Site</label>
            <ng-select
              id="audit-site-multi-drop-down"
              class="sfl-track-dropdown"
              name="Site"
              [multiple]="true"
              [items]="siteAuditSiteList"
              (change)="onFilterChange(true); addFilter(siteAuditSiteList, filterTitleList.SITE_AUDIT_SITE, 'siteNames', 'name')"
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.siteNames"
              [closeOnSelect]="false"
              notFoundText="No Site Found"
              placeholder="Select Site"
              appendTo="body"
              (search)="onFilterSearch($event, 'siteNames', 'name')"
              (close)="filterModelCopy.siteNames = []"
            >
              <ng-template ng-header-tmp *ngIf="siteAuditSiteList && siteAuditSiteList.length">
                <button
                  (click)="
                    selectAndDeselectAll(siteAuditSiteList, 'siteNames', true, 'name');
                    addFilter(siteAuditSiteList, filterTitleList.SITE_AUDIT_SITE, 'siteNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_SITE, true);
                    addFilter(siteAuditSiteList, filterTitleList.SITE_AUDIT_SITE, 'siteNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!--CREW LEAD Multi Select-->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SITE_AUDIT_CREW_MEMBER?.show"
          >
            <label class="label" for="portfolio">Crew Lead / Crew Member</label>
            <ng-select
              id="audit-crew-member-drop-down"
              class="sfl-track-dropdown"
              name="SiteAuditCrewMember"
              [multiple]="true"
              [items]="SiteAuditCrewMemberList"
              (change)="
                onFilterChange(true); addFilter(SiteAuditCrewMemberList, filterTitleList.SITE_AUDIT_CREW_MEMBER, 'crewMemberNames', 'name')
              "
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.crewMemberNames"
              [closeOnSelect]="false"
              notFoundText="No Crew Member Found"
              placeholder="Select Crew Lead / Member"
              appendTo="body"
              (search)="onFilterSearch($event, 'crewMemberNames', 'name')"
              (close)="filterModelCopy.crewMemberNames = []"
            >
              <ng-template ng-header-tmp *ngIf="SiteAuditCrewMemberList && SiteAuditCrewMemberList.length">
                <button
                  (click)="
                    selectAndDeselectAll(SiteAuditCrewMemberList, 'crewMemberNames', true, 'name');
                    addFilter(SiteAuditCrewMemberList, filterTitleList.SITE_AUDIT_CREW_MEMBER, 'crewMemberNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.SITE_AUDIT_CREW_MEMBER, true);
                    addFilter(SiteAuditCrewMemberList, filterTitleList.SITE_AUDIT_CREW_MEMBER, 'crewMemberNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- YEAR -->
          <div
            [ngClass]="filterDetails?.filter_item?.CURRENT_PAGE === 'workOrderPage' ? 'col-xl-1' : 'col-xl-2'"
            class="col-12 col-sm-6 col-md-4 col-lg-3 mb-2 pe-sm-0"
            *ngIf="filterDetails?.filter_item?.START_YEAR?.show && !this.filterDetails.filter_item.START_YEAR?.multi"
          >
            <label class="label" for="input-startYear">Year</label>
            <ng-select
              id="startYear"
              class="sfl-track-dropdown"
              name="startYear"
              [items]="years"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.year"
              notFoundText="No Year Found"
              placeholder="Select Year"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.START_YEAR)"
              [clearable]="false"
              appendTo="body"
            >
            </ng-select>
          </div>

          <!--Crew Lead Multi Select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.CREW_MEMBER?.show && user[0] !== 'customer'"
          >
            <label class="label" for="input-siteId">Crew Lead / Crew Member</label>
            <ng-select
              id="crew-lead-member-drop-down"
              class="sfl-track-dropdown"
              name="Crew Member"
              [multiple]="true"
              [items]="crewMemberList"
              (change)="onFilterChange(true); addFilter(crewMemberList, filterTitleList.CREW_MEMBER, 'crewMemberNames', 'name')"
              bindLabel="name"
              bindValue="name"
              [(ngModel)]="filterModel.crewMemberNames"
              [closeOnSelect]="false"
              notFoundText="No Crew Member Found"
              placeholder="Select Crew Lead / Member"
              appendTo="body"
              (search)="onFilterSearch($event, 'crewMemberNames', 'name')"
              (close)="filterModelCopy.crewMemberNames = []"
            >
              <ng-template ng-header-tmp *ngIf="crewMemberList && crewMemberList.length">
                <button
                  (click)="
                    selectAndDeselectAll(crewMemberList, 'crewMemberNames', true, 'name');
                    addFilter(crewMemberList, filterTitleList.CREW_MEMBER, 'crewMemberNames', 'name')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.CREW_MEMBER, true);
                    addFilter(crewMemberList, filterTitleList.CREW_MEMBER, 'crewMemberNames', 'name')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!--Linked Field-->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.IS_LINK?.show">
            <label class="label d-flex" for="customer"> Linked </label><br />
            <nb-toggle
              id="linked-feild-toggle"
              class="sfl-track-toggle"
              name="isLink"
              [(checked)]="filterModel.isLink"
              (checkedChange)="linkedReport($event)"
              class="marginTop"
              status="primary"
            ></nb-toggle>
          </div>

          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.AUTOMATION_DATA_SOURCE?.show"
          >
            <label class="label" for="input-siteId">Data Source</label>
            <ng-select
              id="data-source-drop-down"
              class="sfl-track-dropdown"
              name="data source"
              [multiple]="true"
              [items]="dataSourceList"
              (change)="onFilterChange(true); addFilter(dataSourceList, filterTitleList.AUTOMATION_DATA_SOURCE, 'automationPartnerIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.automationPartnerIds"
              [closeOnSelect]="false"
              notFoundText="No Data Source Found"
              placeholder="Select Data Source"
              [appendTo]="filterAppendPosition"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              (search)="onFilterSearch($event, 'automationPartnerIds')"
              (close)="filterModelCopy.automationPartnerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="dataSourceList && dataSourceList.length">
                <button
                  (click)="
                    selectAndDeselectAll(dataSourceList, 'automationPartnerIds', true);
                    addFilter(dataSourceList, filterTitleList.AUTOMATION_DATA_SOURCE, 'automationPartnerIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.AUTOMATION_DATA_SOURCE, true);
                    addFilter(dataSourceList, filterTitleList.AUTOMATION_DATA_SOURCE, 'automationPartnerIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.AUTOMATION_SITE?.show">
            <label class="label" for="input-siteId">Automation Site</label>
            <ng-select
              id="automation-site-drop-down"
              class="sfl-track-dropdown"
              name="automation site"
              [multiple]="true"
              [items]="automationSitesList"
              (change)="
                onFilterChange(true); addFilter(automationSitesList, filterTitleList.AUTOMATION_SITE, 'automationSiteIds', 'siteNumber')
              "
              bindLabel="name"
              bindValue="siteNumber"
              [(ngModel)]="filterModel.automationSiteIds"
              [closeOnSelect]="false"
              notFoundText="No Site Found"
              placeholder="Select Site"
              [appendTo]="filterAppendPosition"
              [loading]="filterAppendPosition == 'body' ? false : loading"
              (search)="onFilterSearch($event, 'automationSiteIds', 'siteNumber')"
              (close)="filterModelCopy.automationSiteIds = []"
              [virtualScroll]="true"
            >
              <ng-template ng-header-tmp *ngIf="automationSitesList && automationSitesList.length">
                <button
                  (click)="
                    selectAndDeselectAll(automationSitesList, 'automationSiteIds', true, 'siteNumber');
                    addFilter(automationSitesList, filterTitleList.AUTOMATION_SITE, 'automationSiteIds', 'siteNumber')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.AUTOMATION_SITE, true);
                    addFilter(automationSitesList, filterTitleList.AUTOMATION_SITE, 'automationSiteIds', 'siteNumber')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- QE Service Types -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2 qe-service-types"
            *ngIf="filterDetails?.filter_item?.QE_SERVICE_TYPE?.show"
          >
            <label class="label" for="input-qe-service-types">Service Type</label>
            <ng-select
              id="qe-service-type-drop-down"
              class="sfl-track-dropdown input-qe-service-types"
              name="qeServiceTypes"
              (ngModelChange)="setServiceTypeGroup($event)"
              (change)="onFilterChange(true); addFilter(qeServiceTypeDropDownOption, filterTitleList.QE_SERVICE_TYPE, 'qeServiceTypes')"
              [items]="qeServiceTypeDropDownOption"
              [(ngModel)]="filterModel.qeServiceTypes"
              [multiple]="true"
              bindLabel="name"
              bindValue="id"
              groupBy="serviceTypeGroupName"
              [selectableGroup]="false"
              [selectableGroupAsModel]="true"
              [closeOnSelect]="false"
              (clear)="clearSingleFilter(filterTitleList.QE_SERVICE_TYPE); onServiceTypeGroupChange('', false)"
              (remove)="filterModel.qeServiceTypes.length === 0 && onServiceTypeGroupChange('', false)"
              appendTo="body"
              notFoundText="No Service Types Found"
              placeholder="Select Service Type"
            >
              <ng-template ng-multi-label-tmp let-items="items" let-item$="item$" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.serviceTypeGroupName }} - {{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
              <ng-template ng-optgroup-tmp let-item="item">
                <ng-container *ngIf="getServiceTypeGroupResult(item.serviceTypeGroupName) as groupResult">
                  <input
                    id="item-{{ index }}"
                    name="item-{{ index }}"
                    type="checkbox"
                    [ngModel]="groupResult.selected"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="onServiceTypeGroupChange(item.serviceTypeGroupName, $event)"
                  />
                  {{ item.serviceTypeGroupName }}
                </ng-container>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div [ngClass]="{ 'pe-none opacity-75': item.disabled && !getServiceTypeGroupResult(item.serviceTypeGroupName)?.selected }">
                  <input
                    id="item-{{ index }}"
                    name="item-{{ index }}"
                    type="checkbox"
                    [ngModel]="item$.selected"
                    [disabled]="item.disabled"
                    [ngModelOptions]="{ standalone: true }"
                  />
                  {{ item.name }}
                </div>
              </ng-template>
            </ng-select>
          </div>

          <div class="col-12 col-sm-2 pr-0" *ngIf="filterDetails?.filter_item?.SHOW_ARCHIVED?.show && user[0] !== 'customer'">
            <label *ngIf="filterDetails.page_name !== 'reportListPage'" class="label lbl-archive" for="input-siteId">Archive Toggle</label>
            <nb-checkbox class="archive-toggle" name="isArchive" [checked]="filterModel.isArchive" (change)="archiveUnarchiveReport($event)"
              ><span class="fw-bold">Show Archive</span>
            </nb-checkbox>
          </div>

          <!-- QE Tech/ Field Tech (Work Order) Multi select-->
          <div
            [ngClass]="filterDetails?.filter_item?.CURRENT_PAGE === 'workOrderPage' ? 'col-xl-1' : 'col-xl-2'"
            class="col-12 col-sm-6 col-md-4 col-lg-3 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.FIELD_TECH_IDS?.show"
          >
            <label class="label" for="field-tech-drop-down">Field Tech</label>
            <ng-select
              id="field-tech-drop-down"
              class="sfl-track-dropdown"
              name="fieldTech"
              [multiple]="true"
              [items]="fieldTechList"
              (change)="onFilterChange(true); addFilter(fieldTechList, filterTitleList.FIELD_TECH_IDS, 'FieldTechIds')"
              (clear)="clearSingleFilter(filterTitleList.FIELD_TECH_IDS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.FieldTechIds"
              [closeOnSelect]="false"
              notFoundText="No Field Tech Found"
              placeholder="Field Tech"
              appendTo="body"
              (search)="onFilterSearch($event, 'FieldTechIds')"
              (close)="filterModelCopy.FieldTechIds = []"
            >
              <ng-template ng-header-tmp *ngIf="fieldTechList && fieldTechList.length">
                <button
                  (click)="
                    selectAndDeselectAll(fieldTechList, 'FieldTechIds', true);
                    addFilter(fieldTechList, filterTitleList.FIELD_TECH_IDS, 'FieldTechIds')
                  "
                  class="btn btn-sm btn-primary sfl-select-all-btn"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.FIELD_TECH_IDS, true);
                    addFilter(fieldTechList, filterTitleList.FIELD_TECH_IDS, 'FieldTechIds')
                  "
                  class="btn btn-sm btn-primary ms-1 sfl-unselect-all-btn"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- IS RESCHEDULED TOGGLE -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-1 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.IS_RESCHEDULED?.show">
            <label class="label d-flex" for="rescheduled-toggle"> Rescheduled </label><br />
            <nb-toggle
              id="rescheduled-toggle"
              name="isRescheduled"
              [(checked)]="filterModel.IsRescheduled"
              (checkedChange)="isRescheduled($event)"
              class="marginTop sfl-track-toggle"
              status="primary"
            ></nb-toggle>
          </div>

          <!-- IS UNSCHEDULED TOGGLE -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-1 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.IS_UNSCHEDULED?.show">
            <label class="label d-flex" for="rescheduled-toggle"> Unscheduled </label><br />
            <nb-toggle
              id="rescheduled-toggle"
              name="isRescheduled"
              [(checked)]="filterModel.isUnScheduled"
              (checkedChange)="isUnscheduled($event)"
              class="marginTop sfl-track-toggle"
              status="primary"
            ></nb-toggle>
          </div>

          <!-- IS Tentative Month TOGGLE -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-1 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.IS_TENTATIVE_MONTH?.show">
            <label class="label d-flex" for="tentative-month-toggle"> Tentative </label><br />
            <nb-toggle
              id="tentative-month-toggle"
              name="isTentativeMonth"
              [(checked)]="filterModel.IsTentavieMonth"
              (checkedChange)="isTentativeMonth($event)"
              class="marginTop sfl-track-toggle"
              status="primary"
            ></nb-toggle>
          </div>
          <!-- ticket EstimateStatus MultiSelect -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="
              filterDetails?.filter_item?.TICKET_ESTIMATION_STATUS?.show &&
              this.filterDetails.filter_item.TICKET_ESTIMATION_STATUS?.multi &&
              isBillingPage
            "
          >
            <label class="label" for="input-state">Estimation Status</label>
            <ng-select
              id="Estimation-drop-down"
              class="sfl-track-dropdown"
              name="EstimationStatus"
              [items]="ticketEstimateStatus"
              (change)="
                onFilterChange(true); addFilter(ticketEstimateStatus, filterTitleList.TICKET_ESTIMATION_STATUS, 'ticketEstimateStatusIds')
              "
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.ticketEstimateStatusIds"
              notFoundText="No Estimation Status Found"
              placeholder="Select Estimation Status"
              [multiple]="true"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'ticketEstimateStatusIds')"
              (close)="filterModelCopy.ticketEstimateStatusIds = []"
            >
              <ng-template ng-header-tmp *ngIf="ticketEstimateStatus && ticketEstimateStatus.length">
                <button
                  (click)="
                    selectAndDeselectAll(ticketEstimateStatus, 'ticketEstimateStatusIds', true);
                    addFilter(ticketEstimateStatus, filterTitleList.TICKET_ESTIMATION_STATUS, 'ticketEstimateStatusIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.TICKET_ESTIMATION_STATUS, true);
                    addFilter(ticketEstimateStatus, filterTitleList.TICKET_ESTIMATION_STATUS, 'ticketEstimateStatusIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- single select Start date -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.START_DATE?.show && billingPageViewType === 'dispatchView'"
          >
            <label class="label" for="year">Start Date</label>
            <input
              class="form-control search-textbox"
              [nbDatepicker]="startDate"
              name="availabilityDate"
              placeholder="Select Date"
              id="input-availabilityDate"
              autocomplete="off"
              [(ngModel)]="filterModel.startDate"
              (ngModelChange)="onSingleDateChanged($event); addFilter({ name: filterModel.startDate }, 'Start Date')"
              (clear)="clearSingleFilter(filterTitleList.START_DATE)"
            />
            <nb-datepicker #startDate></nb-datepicker>
          </div>
          <!-- single select End date -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.END_DATE?.show && billingPageViewType === 'dispatchView'"
          >
            <label class="label" for="year">End Date</label>
            <input
              class="form-control search-textbox"
              [nbDatepicker]="endDate"
              name="availabilityDate"
              placeholder="Select Date"
              id="input-availabilityDate"
              autocomplete="off"
              [(ngModel)]="filterModel.endDate"
              (ngModelChange)="onSingleDateChanged($event); addFilter({ name: filterModel.endDate }, 'End Date')"
              (clear)="clearSingleFilter(filterTitleList.END_DATE)"
            />
            <nb-datepicker #endDate></nb-datepicker>
          </div>

          <!-- Template Type For Form Listing -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.TEMPLATE_TYPE_FORM_LIST_IDS?.show"
          >
            <label class="label" for="input-siteId">Template Type</label>
            <ng-select
              id="tepmplate-type-drop-down"
              class="sfl-track-dropdown"
              name="data source"
              [multiple]="false"
              [items]="templateTypeList"
              (change)="
                onFilterChangeAndRemove($event, filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS);
                onFilterChange(true);
                addFilter($event, filterTitleList.TEMPLATE_TYPE_FORM_LIST_IDS)
              "
              bindLabel="templateTypeName"
              bindValue="templateTypeId"
              [(ngModel)]="filterModel.templateTypeId"
              [closeOnSelect]="true"
              notFoundText="No Template type Found"
              placeholder="Select Template type"
              appendTo="body"
              [clearable]="false"
            >
            </ng-select>
          </div>

          <!-- Template Type -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.TEMPLATE_TYPE_IDS?.show">
            <label class="label" for="input-siteId">Template Type</label>
            <ng-select
              id="tepmplate-type-drop-down"
              class="sfl-track-dropdown"
              name="data source"
              [multiple]="true"
              [items]="templateTypeList"
              (change)="
                onFilterChange(true); addFilter(templateTypeList, filterTitleList.TEMPLATE_TYPE_IDS, 'templateTypeIds', 'templateTypeId')
              "
              bindLabel="templateTypeName"
              bindValue="templateTypeId"
              [(ngModel)]="filterModel.templateTypeIds"
              [closeOnSelect]="false"
              notFoundText="No Template type Found"
              placeholder="Select Template type"
              appendTo="body"
              (search)="onFilterSearch($event, 'templateTypeIds')"
              (close)="filterModelCopy.templateTypeIds = []"
            >
              <ng-template ng-header-tmp *ngIf="templateTypeList && templateTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(templateTypeList, 'templateTypeIds', true, 'templateTypeId');
                    addFilter(templateTypeList, filterTitleList.TEMPLATE_TYPE_IDS, 'templateTypeIds', 'templateTypeId')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.TEMPLATE_TYPE_IDS, true);
                    addFilter(templateTypeList, filterTitleList.TEMPLATE_TYPE_IDS, 'templateTypeIds', 'templateTypeId')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.templateTypeName }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.templateTypeName }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Equipment -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="
              filterDetails?.filter_item?.EQUIPMENT_LIST?.show && filterModel.templateTypeId === qestFormTemplateTypes.QEST_INVERTER_PM
            "
          >
            <label class="label" for="input-siteId">Equipment</label>
            <ng-select
              id="equipment-drop-down"
              name="equipment"
              class="sfl-track-dropdown"
              bindLabel="equipmentModel"
              bindValue="id"
              [multiple]="true"
              [items]="equipmentList"
              [(ngModel)]="filterModel.equipmentIds"
              notFoundText="No Equipment Found"
              placeholder="Select Equipment"
              (change)="onFilterChange(true); addFilter(equipmentList, filterTitleList.EQUIPMENT_LIST, 'equipmentIds')"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'equipmentIds')"
              (close)="filterModelCopy.equipmentIds = []"
            >
              <ng-template ng-header-tmp *ngIf="equipmentList && equipmentList.length">
                <button
                  (click)="
                    selectAndDeselectAll(equipmentList, 'equipmentIds', true);
                    addFilter(equipmentList, filterTitleList.EQUIPMENT_LIST, 'equipmentIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.EQUIPMENT_LIST, true);
                    addFilter(equipmentList, filterTitleList.EQUIPMENT_LIST, 'equipmentIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.equipmentModel }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.equipmentModel }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Control Type -->
          <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2" *ngIf="filterDetails?.filter_item?.CONTROL_TYPE_IDS?.show">
            <label class="label" for="control-type-drop-down">Control Type</label>
            <ng-select
              id="control-type-drop-down"
              class="sfl-track-dropdown"
              name="controlType"
              [multiple]="true"
              [items]="controlTypeList"
              (change)="onFilterChange(true); addFilter(controlTypeList, filterTitleList.CONTROL_TYPE_IDS, 'controlTypeIds')"
              (clear)="clearSingleFilter(filterTitleList.CONTROL_TYPE_IDS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.controlTypeIds"
              [closeOnSelect]="false"
              notFoundText="No Control Type Found"
              placeholder="Control Type"
              appendTo="body"
              (search)="onFilterSearch($event, 'controlTypeIds')"
              (close)="filterModelCopy.controlTypeIds = []"
            >
              <ng-template ng-header-tmp *ngIf="controlTypeList && controlTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(controlTypeList, 'controlTypeIds', true);
                    addFilter(controlTypeList, filterTitleList.CONTROL_TYPE_IDS, 'controlTypeIds')
                  "
                  class="btn btn-sm btn-primary sfl-select-all-btn"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.CONTROL_TYPE_IDS, true);
                    addFilter(controlTypeList, filterTitleList.CONTROL_TYPE_IDS, 'controlTypeIds')
                  "
                  class="btn btn-sm btn-primary ms-1 sfl-unselect-all-btn"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Tag Type (CONTROL_DATA_TYPE) -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.CONTROL_DATA_TYPE_IDS?.show"
          >
            <label class="label" for="tag-type-drop-down">Tag Type</label>
            <ng-select
              id="tag-type-drop-down"
              class="sfl-track-dropdown"
              name="controlType"
              [multiple]="true"
              [items]="tagTypeList"
              (change)="onFilterChange(true); addFilter(tagTypeList, filterTitleList.CONTROL_DATA_TYPE_IDS, 'controlDataTypeIds')"
              (clear)="clearSingleFilter(filterTitleList.CONTROL_DATA_TYPE_IDS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.controlDataTypeIds"
              [closeOnSelect]="false"
              notFoundText="No Tag Type Found"
              placeholder="Tag Type"
              appendTo="body"
              (search)="onFilterSearch($event, 'controlDataTypeIds')"
              (close)="filterModelCopy.controlDataTypeIds = []"
            >
              <ng-template ng-header-tmp *ngIf="tagTypeList && tagTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(tagTypeList, 'controlDataTypeIds', true);
                    addFilter(tagTypeList, filterTitleList.CONTROL_DATA_TYPE_IDS, 'controlDataTypeIds')
                  "
                  class="btn btn-sm btn-primary sfl-select-all-btn"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.CONTROL_DATA_TYPE_IDS, true);
                    addFilter(tagTypeList, filterTitleList.CONTROL_DATA_TYPE_IDS, 'controlDataTypeIds')
                  "
                  class="btn btn-sm btn-primary ms-1 sfl-unselect-all-btn"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Ticket Billing Status -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.BILLING_STATUS?.show && user[0] !== 'customer'"
          >
            <label class="label" for="input-date">Billing Status</label>
            <ng-select
              id="billing-status-drop-down"
              class="sfl-track-dropdown"
              name="billingStatuses"
              [items]="ticketBillingStatuses"
              bindLabel="description"
              bindValue="ticketBillingStatusID"
              (change)="
                onFilterChange(true);
                addFilter(ticketBillingStatuses, filterTitleList.BILLING_STATUS, 'TicketBillingStatusIds', 'ticketBillingStatusID')
              "
              (clear)="clearSingleFilter(filterTitleList.BILLING_STATUS)"
              [(ngModel)]="filterModel.TicketBillingStatusIds"
              placeholder="Select Billing Status"
              appendTo="body"
              (search)="onFilterSearch($event, 'TicketBillingStatusIds', 'ticketBillingStatusID')"
              (close)="filterModelCopy.TicketBillingStatusIds = []"
              [multiple]="true"
            >
              <ng-template ng-header-tmp *ngIf="ticketBillingStatuses && ticketBillingStatuses.length">
                <button
                  (click)="
                    selectAndDeselectAll(ticketBillingStatuses, 'TicketBillingStatusIds', true, 'ticketBillingStatusID');
                    addFilter(ticketBillingStatuses, filterTitleList.BILLING_STATUS, 'TicketBillingStatusIds', 'ticketBillingStatusID')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.BILLING_STATUS, true);
                    addFilter(ticketBillingStatuses, filterTitleList.BILLING_STATUS, 'TicketBillingStatusIds', 'ticketBillingStatusID')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.description }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.description }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- Ticket Type Multi select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.TICKET_TYPE?.show && filterDetails.filter_item.TICKET_TYPE.multi"
          >
            <label class="label" for="input-state">Ticket Type</label>
            <ng-select
              id="ticket-type-drop-down"
              class="sfl-track-dropdown"
              name="TicketType"
              [items]="ticketTypeList"
              (change)="onFilterChange(true); addFilter(ticketTypeList, filterTitleList.TICKET_TYPE, 'ticketTypeIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.ticketTypeIds"
              notFoundText="No Ticket Type Found"
              placeholder="Select Ticket Type"
              [multiple]="true"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'ticketTypeIds')"
              (close)="filterModelCopy.customerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="ticketTypeList && ticketTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(ticketTypeList, 'ticketTypeIds', true);
                    addFilter(ticketTypeList, filterTitleList.TICKET_TYPE, 'ticketTypeIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.TICKET_TYPE, true);
                    addFilter(ticketTypeList, filterTitleList.TICKET_TYPE, 'ticketTypeIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <!-- Ticket Type Single select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.TICKET_TYPE?.show && !filterDetails.filter_item.TICKET_TYPE.multi"
          >
            <label class="label" for="input-state">Ticket Type</label>
            <ng-select
              id="ticket-type-single-drop-down"
              class="sfl-track-dropdown"
              name="TicketType"
              [items]="ticketTypeList"
              (change)="refreshListInParent(); addFilter($event, filterTitleList.TICKET_TYPE)"
              (clear)="clearSingleFilter(filterTitleList.TICKET_TYPE)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.ticketTypeId"
              notFoundText="No Ticket Type Found"
              placeholder="Select Ticket Type"
              appendTo="body"
            >
            </ng-select>
          </div>

          <!-- COSTS TYPE Multi select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.COSTS_TYPE?.show && filterDetails.filter_item.COSTS_TYPE.multi"
          >
            <label class="label" for="input-state">Costs</label>
            <ng-select
              id="cost-type-drop-down"
              class="sfl-track-dropdown"
              name="CostType"
              [items]="costTypeList"
              (change)="onFilterChange(true); addFilter(costTypeList, filterTitleList.COSTS_TYPE, 'costTypeIds')"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.costTypeIds"
              notFoundText="No Costs Found"
              placeholder="Select Costs"
              [multiple]="true"
              [closeOnSelect]="false"
              appendTo="body"
              (search)="onFilterSearch($event, 'costTypeIds')"
              (close)="filterModelCopy.customerIds = []"
            >
              <ng-template ng-header-tmp *ngIf="costTypeList && costTypeList.length">
                <button
                  (click)="
                    selectAndDeselectAll(costTypeList, 'costTypeIds', true);
                    addFilter(costTypeList, filterTitleList.COSTS_TYPE, 'costTypeIds')
                  "
                  class="btn btn-sm btn-primary"
                >
                  Select all
                </button>
                <button
                  (click)="
                    clearSingleFilter(filterTitleList.COSTS_TYPE, true); addFilter(costTypeList, filterTitleList.COSTS_TYPE, 'costTypeIds')
                  "
                  class="btn btn-sm btn-primary ms-1"
                >
                  Unselect all
                </button>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" /> {{ item.name }}
              </ng-template>
              <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                <div class="ng-value d-flex" *ngFor="let item of items | slice : 0 : 1">
                  <span
                    class="ng-value-label text-truncate"
                    [ngClass]="{
                      'w-px-48': (null | screenSize) < 1400 && items?.length > 1,
                      'w-px-75': (null | screenSize) < 1400 && item?.name?.length > 5 && items?.length === 1,
                      'w-px-110': (null | screenSize) > 1400 && items?.length >= 1
                    }"
                    >{{ item.name }}</span
                  >
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
            </ng-select>
          </div>

          <!-- COSTS TYPE Single select -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.COSTS_TYPE?.show && !filterDetails.filter_item.COSTS_TYPE.multi"
          >
            <label class="label" for="input-state">Costs</label>
            <ng-select
              id="cost-type-single-drop-down"
              class="sfl-track-dropdown"
              name="CostType"
              [items]="costTypeList"
              (change)="refreshListInParent(); addFilter($event, filterTitleList.COSTS_TYPE)"
              (clear)="clearSingleFilter(filterTitleList.COSTS_TYPE)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.costTypeId"
              notFoundText="No Costs Found"
              placeholder="Select Costs"
              appendTo="body"
            >
            </ng-select>
          </div>

          <!-- Site Audit Report Status For Site Audit Report Listing -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2"
            *ngIf="filterDetails?.filter_item?.SITE_AUDIT_REPORT_STATUS?.show"
          >
            <label class="label" for="input-status">Status</label>
            <ng-select
              id="sa-report-status-type-drop-down"
              class="sfl-track-dropdown"
              name="saReportStatusId"
              [items]="saReportStatusList"
              (change)="onFilterChange(true); addFilter($event, filterTitleList.SITE_AUDIT_REPORT_STATUS)"
              (clear)="clearSingleFilter(filterTitleList.SITE_AUDIT_REPORT_STATUS)"
              bindLabel="name"
              bindValue="id"
              [(ngModel)]="filterModel.reportStatusId"
              notFoundText="No Status Found"
              placeholder="Select Status"
              appendTo="body"
            >
            </ng-select>
          </div>

          <!-- QE Modules -->
          <div
            class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2 qe-modules"
            *ngIf="filterDetails?.filter_item?.QE_MODULES?.show"
          >
            <label class="label" for="input-qe-modules">Moldues</label>
            <ng-select
              id="qe-qe-modules-drop-down"
              class="sfl-track-dropdown input-qe-modules"
              name="qeMenuModuleIds"
              [items]="qeMenuModuleTypeList"
              [(ngModel)]="filterModel.qeMenuModuleIds"
              (change)="onFilterChange(true); addFilter(qeMenuModuleTypeList, filterTitleList.QE_MODULES, 'qeMenuModuleIds')"
              (clear)="clearSingleFilter(filterTitleList.QE_MODULES, true)"
              [multiple]="true"
              bindLabel="name"
              bindValue="id"
              groupBy="parentName"
              [selectableGroup]="true"
              [selectableGroupAsModel]="false"
              [closeOnSelect]="false"
              appendTo="body"
              notFoundText="No Modules Found"
              placeholder="Select Modules"
            >
              <ng-template ng-multi-label-tmp let-items="items" let-item$="item$" let-clear="clear">
                <div class="ng-value" *ngFor="let item of items | slice : 0 : 1">
                  <span class="ng-value-label">{{ item.parentName }} - {{ item.name }}</span>
                  <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
                </div>
                <div class="ng-value" *ngIf="items.length > 1">
                  <span class="ng-value-label">+{{ items.length - 1 }} </span>
                </div>
              </ng-template>
              <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                <div [style.paddingLeft.px]="(item$?.children[0]?.value?.menuLevelOrder - 1) * 12">
                  <input
                    id="item-{{ index }}"
                    name="item-{{ index }}"
                    type="checkbox"
                    [ngModel]="item$.selected"
                    [ngModelOptions]="{ standalone: true }"
                  />
                  {{ item.parentName }}
                </div>
              </ng-template>
              <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                <div [style.paddingLeft.px]="(item.menuLevelOrder - 1) * 12">
                  <input
                    id="item-{{ index }}"
                    name="item-{{ index }}"
                    type="checkbox"
                    [ngModel]="item$.selected"
                    [ngModelOptions]="{ standalone: true }"
                  />
                  {{ item.name }}
                </div>
              </ng-template>
            </ng-select>
          </div>

          <ng-container *ngIf="filterDetails?.filter_item?.QE_DATE_DURATION?.show">
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2 qe-date-duration">
              <label class="label" for="qe-date-duration">Duration</label>
              <ng-select
                id="qe-date-duration"
                name="qe-date-duration"
                [items]="dateDurationTypeList"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="filterModel.dateDuration"
                notFoundText="No Duration Found"
                placeholder="Select Duration"
                [clearable]="false"
                (change)="onDateDurationSelect(); onFilterChange(true); addFilter($event, filterTitleList.QE_DATE_DURATION)"
                appendTo="body"
              >
              </ng-select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2 pe-sm-0 mb-2">
              <div class="row m-0">
                <div
                  class="col-6 p-0 pe-2"
                  [ngClass]="{
                    'col-6':
                      filterModel.dateDuration !== qeDateDurationKeyEnum.SPECIFIC_DATE &&
                      filterModel.dateDuration !== qeDateDurationKeyEnum.CURRENT_DAY &&
                      filterModel.dateDuration !== qeDateDurationKeyEnum.PREVIOUS_DAY,
                    'col-12':
                      filterModel.dateDuration === qeDateDurationKeyEnum.SPECIFIC_DATE ||
                      filterModel.dateDuration === qeDateDurationKeyEnum.CURRENT_DAY ||
                      filterModel.dateDuration === qeDateDurationKeyEnum.PREVIOUS_DAY
                  }"
                >
                  <label
                    class="label"
                    for="qe-duration-start-date"
                    *ngIf="
                      filterModel.dateDuration !== qeDateDurationKeyEnum.SPECIFIC_DATE &&
                      filterModel.dateDuration !== qeDateDurationKeyEnum.CURRENT_DAY &&
                      filterModel.dateDuration !== qeDateDurationKeyEnum.PREVIOUS_DAY
                    "
                  >
                    Start Date
                  </label>
                  <label
                    class="label"
                    for="qe-duration-start-date"
                    *ngIf="
                      filterModel.dateDuration === qeDateDurationKeyEnum.SPECIFIC_DATE ||
                      filterModel.dateDuration === qeDateDurationKeyEnum.CURRENT_DAY ||
                      filterModel.dateDuration === qeDateDurationKeyEnum.PREVIOUS_DAY
                    "
                  >
                    Date
                  </label>
                  <input
                    class="form-control qe-duration-start-date search-textbox"
                    [nbDatepicker]="durationStartDate"
                    name="qe-duration-start-date"
                    placeholder="Select Date"
                    id="iqe-duration-start-date"
                    autocomplete="off"
                    [(ngModel)]="filterModel.durationStartDate"
                    (ngModelChange)="onDateDurationDateChanged($event)"
                    [disabled]="
                      !(
                        filterModel.dateDuration === qeDateDurationKeyEnum.SPECIFIC_DATE ||
                        filterModel.dateDuration === qeDateDurationKeyEnum.DATE_RANGE
                      )
                    "
                  />
                  <nb-datepicker #durationStartDate [max]="dateDurationConfig.durationMaxDate"></nb-datepicker>
                </div>
                <div
                  class="col-6 p-0 ps-1"
                  *ngIf="
                    filterModel.dateDuration !== qeDateDurationKeyEnum.SPECIFIC_DATE &&
                    filterModel.dateDuration !== qeDateDurationKeyEnum.CURRENT_DAY &&
                    filterModel.dateDuration !== qeDateDurationKeyEnum.PREVIOUS_DAY
                  "
                >
                  <label class="label" for="qe-duration-end-date">End Date</label>
                  <input
                    class="form-control qe-duration-end-date search-textbox"
                    [nbDatepicker]="durationEndDate"
                    name="qe-duration-end-date"
                    placeholder="Select Date"
                    id="qe-duration-end-date"
                    autocomplete="off"
                    [(ngModel)]="filterModel.durationEndDate"
                    [disabled]="
                      !(
                        filterModel.dateDuration === qeDateDurationKeyEnum.SPECIFIC_DATE ||
                        filterModel.dateDuration === qeDateDurationKeyEnum.DATE_RANGE
                      )
                    "
                  />
                  <nb-datepicker
                    #durationEndDate
                    [min]="dateDurationConfig.durationMinDate"
                    [max]="dateDurationConfig.durationMaxEndDate"
                  ></nb-datepicker>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </nb-card-body>
  </nb-card>
</div>
