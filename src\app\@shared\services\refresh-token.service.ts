import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { StorageService } from './storage.service';
import { ApiUrl, AppConstants } from '../constants';
import { LoginTokenResponse } from '../../@auth/models/user-authentication.model';

@Injectable({
  providedIn: 'root'
})
export class RefreshTokenService {
  private refreshTokenTimeout!: NodeJS.Timeout;

  constructor(private readonly httpClient: HttpClient, private readonly storageService: StorageService) {}

  startRefreshTokenTimer(accessToken: string) {
    // parse json object from base64 encoded jwt token
    const jwtToken = JSON.parse(atob(accessToken.split('.')[1]));

    // set a timeout to refresh the token five minutes before it expires
    const expires = new Date(jwtToken.exp * 1000);
    const timeout = expires.getTime() - Date.now() - 5 * 60 * 1000;
    this.refreshTokenTimeout = setTimeout(() => this.refreshToken().subscribe(), timeout);
  }

  stopRefreshTokenTimer() {
    clearTimeout(this.refreshTokenTimeout);
  }

  refreshToken(): Observable<LoginTokenResponse | undefined> {
    const id_Token = this.storageService.get(AppConstants.authenticationToken);
    const refresh_Token = this.storageService.get(AppConstants.refreshToken);
    return this.httpClient.post<LoginTokenResponse>(ApiUrl.refreshToken, { id_Token, refresh_Token }).pipe(
      map(response => {
        this.storeAuthTokens(response);
        this.startRefreshTokenTimer(response.id_Token);
        return response;
      })
    );
  }

  storeAuthTokens(loginTokenResponse: LoginTokenResponse) {
    this.storageService.set(AppConstants.authenticationToken, loginTokenResponse.id_Token);
    this.storageService.set(AppConstants.refreshToken, loginTokenResponse.refresh_Token);
    this.storageService.set(AppConstants.isMFAUserLoggedIn, loginTokenResponse.isMFAUserLoggedIn);
  }
}
