import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { UserProfileComponent } from './user-profile/user-profile.component';
import { QEAnalyticsGatheringGuard } from '../qe-analytics/services/qe-analytics-gathering.guard';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';

export const routes: Routes = [
  {
    path: '',
    component: UserProfileComponent,
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      pageTitle: 'User Profile',
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.USER_PROFILE,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OTHERS
    }
  },
  {
    path: 'change-password',
    component: ChangePasswordComponent,
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      pageTitle: 'Change Password',
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.OTHERS
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProfileRoutingModule {}
