import { AppConstants } from '../../constants/app.constant';
import { QE_DATE_DURATION_KEY_ENUM } from './filter.model';

export class CommonFilter {
  public openDate?: { start: Date | string; end: Date | string };
  public date?: { start: Date | string; end: Date | string };
  public closeDate?: { start: Date | string; end: Date | string };
  public activityRange?: { start: Date | string; end: Date | string };
  public exclusionTo?: { start: Date | string; end: Date | string };
  public exclusionFrom?: { start: Date | string; end: Date | string };
  public customerIds?: number[] = [];
  public crewLeadIds?: number[] = [];
  public portfolioIds?: number[] = [];
  public siteIds?: number[] = [];
  public isLink?: boolean;
  public checkInOnly?: boolean;
  public deviceLabel: string;
  public priorityIds?: number[] = [];
  public states?: string[] = [];
  public statusIds?: number[] = [];
  public page? = 0;
  public sortBy? = 'Open';
  public direction? = 'asc';
  public itemsCount? = +AppConstants.rowsPerPage;
  public isExport? = false;
  public searchBy? = 'All';
  public searchValue? = '';
  public ticketStatus?: number;
  public affectedkw?: number;
  public priorityId?: number;
  public showTickets? = true;
  public showEmployees? = false;
  public customerId?: number;
  public siteId?: number;
  public userIds?: number[] = [];
  public regionIds?: number[] = [];
  public subregionIds?: number[] = [];
  public getAllStatusListBetweenDates? = true;
  public years: number[] = [];
  public year: number;
  public state = null;
  public reportTypeIds: number[] = [];
  public frequencies: string[] = [];
  public reportStatus: number = null;
  public woStatus: number = null;
  public portfolioId = null;
  public arrayTypeIds: number[] = [];
  public search: string;
  public deviceTypeIds: number[] = [];
  public deviceModels: string[] = [];
  public mfgs: string[] = [];
  public DeviceName: string;
  public isDelete = false;
  public isArchive = false;
  public IsExportExcel = false;
  public activityRangeStart?: Date | string;
  public activityRangeEnd?: Date | string;
  public truckRoll: number[] = [];
  public isResolve: string;
  public rmaComplete: string;
  public rmaTracking: string;
  public rmaReturnRequired: string;
  public isActive: string;
  public isNERC: string;
  public portfolioNames: string[] = [];
  public customerNames: string[] = [];
  public siteNames: string[] = [];
  public automationPartnerIds: number[] = [];
  public automationSiteIds: number[] = [];
  public crewMemberNames?: string[] = [];
  public productionLoss: boolean = null;
  public IsSiteAuditJHA?: boolean = false;
  public isInclusiveSearch?: boolean = false;
  public IsRescheduled?: boolean = false;
  public isUnScheduled?: boolean = false;
  public IsTentavieMonth?: boolean = false;
  public FieldTechIds: number[] = [];
  public ticketEstimateStatusIds?: number[] = [];
  public startDate: Date | string;
  public endDate: Date | string;
  public templateTypeIds: number[] = [];
  public templateTypeId: number;
  public equipmentIds: number[] = [];
  public TicketBillingStatusIds: number[] = [];
  public controlTypeIds: number[] = [];
  public controlDataTypeIds: number[] = [];
  public ticketTypeId?: number;
  public ticketTypeIds?: number[] = [];
  public costTypeIds?: number[] = [];
  public costTypeId: number = null;
  public reportStatusId: number = null;
  public nercSiteTypeId: number = null;
  public qeServiceTypes: number[] = [];
  public userTypeId?: number = null;
  public userTypeIds?: number[] = [];
  public qeMenuModuleIds?: number[] = [];
  public dateDuration?: QE_DATE_DURATION_KEY_ENUM;
  public durationStartDate?: Date;
  public durationEndDate?: Date;
}
