<nb-card class="reports" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <nb-card-header class="d-flex align-items-center">
    <h6 class="w-100">{{ reportMasterList?.reportTitle }}</h6>
    <div class="d-flex align-items-center w-100">
      <div class="ms-auto d-flex button_list" *ngIf="reportMasterList?.reportPdf === null && reportMasterList?.reportPpt === null">
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          nbTooltip="Create NC Ticket"
          *ngIf="reportMasterList?.assesmentType === 'MVPM'"
          nbTooltipPlacement="top"
          nbTooltipStatus="primary"
          class="ms-1 ms-sm-2"
          [disabled]="loading"
          (click)="openNonConformanceBulkTicketCreateModal($event)"
        >
          Create NC Ticket
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="!viewdeletetedbutton"
          [disabled]="loading"
          class="ms-1 ms-sm-2"
          nbTooltip="Save Changes"
          nbTooltipPlacement="top"
          nbTooltipStatus="info"
          (click)="saveChanges()"
        >
          <span class="d-none d-lg-inline-block">Save Changes</span>
          <i class="d-inline-block d-lg-none fa fa-floppy-o"></i>
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="!viewdeletetedbutton"
          [disabled]="loading"
          class="ms-1 ms-sm-2"
          nbTooltip="Preview Report"
          nbTooltipPlacement="top"
          nbTooltipStatus="info"
          (click)="previewReport(reportMasterList?.workorderId, template)"
        >
          <span class="d-none d-lg-inline-block"> Preview Report</span>
          <i class="d-inline-block d-lg-none fa-regular fa-eye"></i>
        </button>
        <button
          nbButton
          status="primary"
          size="medium"
          type="button"
          *ngIf="!viewdeletetedbutton"
          [disabled]="loading"
          class="ms-1 ms-sm-2"
          nbTooltip="Generate PDF Report"
          nbTooltipPlacement="top"
          nbTooltipStatus="info"
          (click)="generateReport(reportMasterList?.reports[0].reportGuid)"
        >
          <span class="d-none d-lg-inline-block"> <em class="fa fa-file-download download_icon me-2"></em> Generate Report</span>
          <i class="d-inline-block d-lg-none fa fa-file-download download_icon"></i>
        </button>
        <button
          nbButton
          status="basic"
          [disabled]="loading"
          (click)="viewDeletedListing(viewdeletetedbutton)"
          size="medium"
          class="ms-1 ms-sm-2"
        >
          <span class="d-none d-lg-inline-block">Back</span>
          <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
        </button>
      </div>
      <div class="ms-auto d-flex button_list" *ngIf="reportMasterList?.reportPdf !== null || reportMasterList?.reportPpt !== null">
        <div class="d-flex align-items-center">
          <div class="ms-auto d-flex button_list text-center me-2">
            <fieldset class="p-2 border border-1 border-dark" *ngIf="!viewdeletetedbutton">
              <legend class="text-center fs-6">Download Report</legend>
              <button nbButton status="default" size="medium" type="button" (click)="downloadReport(reportMasterList?.workorderId)">
                <em
                  class="fa fa-file-pdf download_icon download_pdf"
                  nbTooltip="Download PDF Report"
                  nbTooltipPlacement="top"
                  nbTooltipStatus="danger"
                ></em>
              </button>
            </fieldset>
          </div>
          <div class="ms-auto d-flex button_list">
            <button
              nbButton
              status="primary"
              size="medium"
              type="button"
              nbTooltip="Create NC Ticket"
              *ngIf="reportMasterList?.assesmentType === 'MVPM'"
              nbTooltipPlacement="top"
              nbTooltipStatus="primary"
              class="mx-1 mx-sm-2"
              [disabled]="loading"
              (click)="openNonConformanceBulkTicketCreateModal($event)"
            >
              Create NC Ticket
            </button>
            <button
              nbButton
              status="primary"
              size="medium"
              *ngIf="!viewdeletetedbutton"
              type="button"
              class="btn-block mx-1 mx-sm-2"
              nbTooltip="Save Changes"
              nbTooltipPlacement="top"
              nbTooltipStatus="info"
              (click)="saveChanges()"
            >
              <span class="d-none d-lg-inline-block">Save Changes</span>
              <i class="d-inline-block d-lg-none fa fa-floppy-o"></i>
            </button>
            <button nbButton status="basic" (click)="viewDeletedListing(viewdeletetedbutton)" size="medium" class="btn-block mx-1 mx-sm-2">
              <span class="d-none d-lg-inline-block">Back</span>
              <i class="d-inline-block d-lg-none fa-solid fa-arrow-left"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <div class="form-control-group">
      <div class="col-12 table-responsive table-card-view">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th class="text-center" id="customerName">Customer Name</th>
              <th class="text-center" id="portfolioName">Portfolio Name</th>
              <th class="text-center" id="siteName">Site Name</th>
              <th class="text-center" id="workOrder">Work Order</th>
              <th class="text-center" id="workOrderStatus">Work Order Status</th>
              <th class="text-center" id="totalReports">Total Reports</th>
              <th class="text-center" id="imageGallery">Image Gallery</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td data-title="Customer Name" class="text-center">
                <a [routerLink]="['/entities/customers/edit/' + reportMasterList?.customerId]"> {{ reportMasterList?.customerName }}</a>
              </td>
              <td data-title="Portfolio Name" class="text-center">
                <a [routerLink]="['/entities/portfolios/edit/' + reportMasterList?.portfolioId]">
                  {{ reportMasterList?.portfolioName }}
                </a>
              </td>
              <td data-title="Site Name" class="text-center">
                <a [routerLink]="['/entities/sites/view/' + reportMasterList?.siteId]">
                  {{ reportMasterList?.siteName }}
                </a>
              </td>
              <td data-title="Work Order" class="text-center">
                <a
                  *ngIf="workOrderList.length <= 1"
                  [routerLink]="['/entities/workorders/add']"
                  [queryParams]="{
                    id: reportMasterList?.assesmentId,
                    assementType: reportMasterList?.assesmentType,
                    frequencyType: reportMasterList?.frequencyType
                  }"
                >
                  {{ reportMasterList?.workorderName }}</a
                >
                <ng-select
                  *ngIf="workOrderList.length > 1"
                  name="workorder"
                  [items]="workOrderList"
                  (change)="onWorkOrderSelect($event)"
                  bindLabel="name"
                  bindValue="id"
                  [(ngModel)]="reportMasterList.workorderId"
                  [clearable]="false"
                  appendTo="body"
                >
                </ng-select>
              </td>
              <td data-title="Work Order Status" class="text-center">
                <label class="label">{{ reportMasterList?.workorderStatus }}</label>
              </td>
              <td data-title="Total Reports" class="text-center">
                <label class="label">{{ reportMasterList?.reportCount }}</label>
              </td>
              <td data-title="Image Gallery" class="text-center cursor-pointer">
                <a class="px-1 text-primary listgrid-icon" (click)="onImageGallery()">
                  <em class="fa fa-images" nbTooltip="Image Gallery" nbTooltipPlacement="top" nbTooltipStatus="primary"></em>
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <nb-tabset fullWidth>
      <nb-tab tabTitle="Job Hazard Analysis (JHA)">
        <sfl-jobhazard *ngIf="reportMasterList?.versionId === 2" [jhaDatas]="reportList" (descriptionWork)="(selectedItem)"></sfl-jobhazard>
        <sfl-jhathree *ngIf="reportMasterList?.versionId === 3" [jhaDatas]="reportList" [jhaList]="jhaDetail"></sfl-jhathree>
      </nb-tab>
      <nb-tab tabTitle="Checklist">
        <sfl-mvpm-checklist [checklist]="reportList"></sfl-mvpm-checklist>
      </nb-tab>
      <nb-tab tabTitle="Equipment Status">
        <sfl-mvpm-equipment-status
          [masterReportDataList]="reportList"
          [siteId]="reportMasterList?.siteId"
          [workId]="reportMasterList?.workorderId"
        ></sfl-mvpm-equipment-status
      ></nb-tab>
      <nb-tab tabTitle="Riser poles"> <sfl-mvpm-riser [masterReportDataList]="reportList"> </sfl-mvpm-riser> </nb-tab>
      <nb-tab tabTitle="Non-Conformance">
        <sfl-nonconformance
          [masterReportDataList]="reportList"
          [reportDetails]="reportMasterList"
          (ncItemAddEditChange)="getById(workorderId)"
          (siteReportImagesCloseEvent)="getById(workorderId)"
          (nCOrderChange)="nCOrderChangeSave = $event"
        ></sfl-nonconformance>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
</nb-card>
<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Preview</h4>
  </div>
  <div class="modal-body preview-body">
    <div #divLegalNoticeHeight [innerHTML]="previewHTML | safeHTML"></div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary" #closeModalButton (click)="previewModalRef.hide()">Close</button>
  </div>
</ng-template>
