import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { NgForm } from '@angular/forms';
import { DropboxImageGalleryService } from '../../../@shared/services/dropbox-image-gallery.service';
import { AlertService } from '../../../@shared/services';
import { NotesAddEdit, NotesEntityName, NotesEntityType, NotesModuleNameForShowUp } from '../notes-management.model';
import { NotesManagementService } from '../notes-management.service';
import { NotesManagementHelperService } from '../notes-management-helper.service';

@Component({
  selector: 'sfl-notes-add-edit',
  templateUrl: './notes-add-edit.component.html',
  styleUrls: ['./notes-add-edit.component.scss']
})
export class NotesAddEditComponent implements OnInit {
  @Input() entityId = 0;
  @Input() entityTypeId: NotesEntityType = NotesEntityType.NONE;
  @Input() entityTypeName: string = NotesEntityName[NotesEntityType.NONE];
  @Input() isNotesViewMode: boolean;
  @Input() notesEntityDetails: NotesAddEdit;
  @Input() isEntityViewMode: boolean;
  @ViewChild('notesAddEditForm') notesAddEditForm: NgForm;
  subscription: Subscription = new Subscription();
  public onClose: Subject<boolean> = new Subject();
  modalRef: BsModalRef;
  notesAddEditLoading = false;
  noteTagList = [];
  noteTagIds = [];
  filteredAppliedTags = [];
  title = '';

  constructor(
    private readonly _bsReschedulerModalRef: BsModalRef,
    private readonly alertService: AlertService,
    private readonly notesManagementHelperService: NotesManagementHelperService
  ) {}

  ngOnInit(): void {
    console.log({ notesEntityDetails: this.notesEntityDetails });
    this.title = this.notesEntityDetails.id ? 'Update Note' : 'Create Note';
    if (this.isNotesViewMode) this.title = `${NotesModuleNameForShowUp[this.entityTypeId]} Note`;
    this.getNoteTags();
  }

  onHide(flag: boolean): void {
    this.onClose.next(flag);
    this._bsReschedulerModalRef.hide();
  }

  onSubmitNote(notesAddEditForm: NgForm): void {
    if (notesAddEditForm.invalid) {
      notesAddEditForm.form.markAllAsTouched();
      return;
    }
    this.notesAddEditLoading = true;
    this.subscription.add(
      this.notesManagementHelperService.addUpdateNote(this.entityTypeId, this.notesEntityDetails).subscribe({
        next: () => {
          const alertMessage = this.notesEntityDetails.id
            ? `${NotesModuleNameForShowUp[this.entityTypeId]} Note updated successfully`
            : `${NotesModuleNameForShowUp[this.entityTypeId]} Note added successfully`;
          this.alertService.showSuccessToast(`${alertMessage}`);
          this.notesAddEditLoading = false;
          this.onHide(true);
        },
        error: () => {
          this.notesAddEditLoading = false;
        }
      })
    );
  }

  getNoteTags(): void {
    this.notesAddEditLoading = true;
    this.notesManagementHelperService.getNoteTagList(this.entityTypeId).subscribe({
      next: data => {
        if (data) {
          this.noteTagList = data;
        }
        this.notesAddEditLoading = false;
      },
      error: e => {
        this.notesAddEditLoading = false;
      }
    });
  }

  onFilter(event): void {
    if (event.term) {
      this.filteredAppliedTags = event.items?.map(element => element.id);
    } else {
      this.filteredAppliedTags = [];
    }
  }

  toggleSelectUnselectAllTags(isSelect = false) {
    if (isSelect) {
      if (!this.filteredAppliedTags.length) {
        this.notesEntityDetails.tags = this.noteTagList.map(noteTag => noteTag.id);
      } else {
        if (!Array.isArray(this.notesEntityDetails.tags)) {
          this.notesEntityDetails.tags = [];
        }
        this.notesEntityDetails.tags = [
          ...new Set([...this.notesEntityDetails.tags, ...JSON.parse(JSON.stringify(this.filteredAppliedTags))])
        ];
      }
    } else {
      if (this.filteredAppliedTags.length) {
        this.notesEntityDetails.tags = this.notesEntityDetails.tags.filter(x => !this.filteredAppliedTags.includes(x));
      } else {
        this.notesEntityDetails.tags = [];
      }
    }
  }

  getTagList(tags: []): string {
    return tags.join(', ');
  }
}
