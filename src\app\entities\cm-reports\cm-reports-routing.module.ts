import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { AuditDispatchDetailComponent } from './audit-dispatch-detail/audit-dispatch-detail.component';
import { AuditDispatchComponent } from './audit-dispatch/audit-dispatch.component';
import { BillingComponent } from './billing/billing.component';
import { CmReportsComponent } from './cm-reports.component';
import { ExclusionListingComponent } from './exclusion-listing/exclusion-listing.component';
import { MapComponent } from './map/map.component';
import { RMAReportComponent } from './rma-report/rma-report.component';
import { TruckRollComponent } from './truck-roll/truck-roll.component';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';
import { QEAnalyticsGatheringGuard } from '../qe-analytics/services/qe-analytics-gathering.guard';

const routes: Routes = [
  {
    path: '',
    component: CmReportsComponent,
    children: [
      {
        path: '',
        redirectTo: 'audit-dispatch',
        data: { pageTitle: 'CM Audit Dispatch' }
      },
      {
        path: 'audit-dispatch',
        component: AuditDispatchComponent,
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          pageTitle: 'CM Audit Dispatch',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'audit-dispatch/list',
        component: AuditDispatchDetailComponent,
        canActivate: [PermissionGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          pageTitle: 'CM Audit Dispatches',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'map',
        component: MapComponent,
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          pageTitle: 'CM Map Report',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_MAP_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'exclusion',
        component: ExclusionListingComponent,
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          pageTitle: 'CM Exlusions',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'billing',
        component: BillingComponent,
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst'],
          pageTitle: 'CM Billing',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'truckroll',
        component: TruckRollComponent,
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'analyst', 'customer'],
          pageTitle: 'CM Truck Roll',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      },
      {
        path: 'rma-report',
        component: RMAReportComponent,
        canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
        data: {
          permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
          pageTitle: 'CM RMA Report',
          qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.CM_RMA_REPORT,
          qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE
        }
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CmReportsRoutingModule {}
