import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QEAnalyticsGatheringGuard } from '../qe-analytics/services/qe-analytics-gathering.guard';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';

const routes: Routes = [
  {
    path: 'dashboard',
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AVB_DASHBOARD,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.AVAILABILITY
    }
  },
  {
    path: 'data-table',
    loadChildren: () => import('./data-table/data-table.module').then(m => m.DataTableModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AVB_DATA_TABLE,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.AVAILABILITY
    }
  },
  {
    path: 'exclusions',
    loadChildren: () => import('./exclusions/exclusions.module').then(m => m.ExclusionsModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.AVAILABILITY
    }
  },
  {
    path: 'reports',
    loadChildren: () => import('./reports/reports.module').then(m => m.ReportsModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.AVB_REPORTS,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.AVAILABILITY
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AvailabilityRoutingModule {}
