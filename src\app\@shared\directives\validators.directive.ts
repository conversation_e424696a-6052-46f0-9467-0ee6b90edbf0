import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { AbstractControl, FormControl, NG_VALIDATORS, NgModel, ValidationErrors, Validator, ValidatorFn } from '@angular/forms';

@Directive({
  selector: '[sflValidators]'
})
export class ValidatorsDirective {
  private readonly el: HTMLInputElement;

  // Allow decimal numbers and negative values
  private readonly regex = new RegExp(/[\d.-]+$/);
  // Allow key codes for special events. Reflect :
  // Backspace, tab, end, home
  private readonly specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', '-', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];

  constructor(private readonly elementRef: ElementRef, private readonly ngModel: NgModel) {
    this.el = this.elementRef.nativeElement;
  }

  @HostListener('keydown', ['$event']) onkeydown(event) {
    // Allow Backspace, tab, end, and home keys
    const value = (<HTMLInputElement>event.target).value;
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
    if (event.keyCode === 110 || event.keyCode === 190) {
      if (value.toString().includes('.')) {
        event.preventDefault();
      }
    }
    const current = this.elementRef.nativeElement.value;
    const position = this.elementRef.nativeElement.selectionStart;
    const next = [current.slice(0, position), event.key === 'Decimal' ? '.' : event.key, current.slice(position)].join('');
    if (next && !String(next).match(this.regex)) {
      event.preventDefault();
    }
  }
}

@Directive({
  selector: '[sflNoSpaceValidate][ngModel]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: RequiredNoSpaceValidator,
      multi: true
    }
  ]
})
export class RequiredNoSpaceValidator implements Validator {
  validator: ValidatorFn;
  constructor() {
    this.validator = validateNoSpaceFactory();
  }
  validate(c: FormControl) {
    return this.validator(c);
  }
}

// validation function
function validateNoSpaceFactory(): ValidatorFn {
  return (c: AbstractControl) => {
    if (c.value && typeof c.value === 'string') {
      if (c.value.trim() !== '') {
        return null;
      } else {
        return {
          appNoSpaceValidate: true
        };
      }
    } else if (c.value !== null) {
      return null;
    } else {
      return {
        appNoSpaceValidate: true
      };
    }
  };
}

@Directive({
  selector: '[sflPhoneValidate][ngModel]',
  providers: [{ provide: NG_VALIDATORS, useExisting: PhoneValidator, multi: true }]
})
export class PhoneValidator implements Validator {
  validator: ValidatorFn;
  constructor() {
    this.validator = validatePhoneFactory();
  }
  validate(c: FormControl) {
    return this.validator(c);
  }
}

// validation function
function validatePhoneFactory(): ValidatorFn {
  return (c: AbstractControl) => {
    if (c.value) {
      if (c.value.match(/^[89]\d{9}$/) || c.value.match(/^[6]\d{9}$/)) {
        return null;
      } else {
        return {
          appPhoneValidate: true
        };
      }
    }
  };
}

@Directive({
  selector: '[appEmailValidate][ngModel]',
  providers: [{ provide: NG_VALIDATORS, useExisting: EmailValidator, multi: true }]
})
export class EmailValidator implements Validator {
  validator: ValidatorFn;
  constructor() {
    this.validator = validateEmailFactory();
  }
  validate(c: FormControl) {
    return this.validator(c);
  }
}

// validation function
function validateEmailFactory(): ValidatorFn {
  return (c: AbstractControl) => {
    if (c.value) {
      // tslint:disable-next-line:max-line-length
      if (c.value.match('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')) {
        return null;
      } else {
        return {
          appEmailValidate: true
        };
      }
    }
  };
}

@Directive({
  selector: '[noLeadingSpace]'
})
export class NoLeadingSpaceDirective {
  constructor(private el: ElementRef) {}

  @HostListener('input', ['$event']) onInput(event: Event): void {
    const inputElement = this.el.nativeElement as HTMLInputElement;
    const inputValue = inputElement.value;

    if (inputValue.startsWith(' ')) {
      inputElement.value = inputValue.trimStart();
      inputElement.dispatchEvent(new Event('input'));
    }
  }
}

@Directive({
  selector: '[NeedDifferentValue]',
  providers: [{ provide: NG_VALIDATORS, useExisting: NeedDifferentValueValidator, multi: true }]
})
export class NeedDifferentValueValidator implements Validator {
  @Input('NeedDifferentValue') compareValue: string;

  validate(control: AbstractControl): ValidationErrors | null {
    const value = control.value || '';
    if ((value != null || value != '') && (this.compareValue != null || this.compareValue != '') && value == this.compareValue) {
      return { needDifferentValue: true };
    }
    return null;
  }
}

@Directive({
  selector: '[NeedSameValue]',
  providers: [{ provide: NG_VALIDATORS, useExisting: NeedSameValueValidator, multi: true }]
})
export class NeedSameValueValidator implements Validator {
  @Input('NeedSameValue') compareValue: string;

  validate(control: AbstractControl): ValidationErrors | null {
    const value = control.value || '';
    if ((value != null || value != '') && (this.compareValue != null || this.compareValue != '') && value != this.compareValue) {
      return { needSameValue: true };
    }
    return null;
  }
}
