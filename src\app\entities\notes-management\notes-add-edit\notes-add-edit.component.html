<div class="alert-box" [nbSpinner]="notesAddEditLoading" nbSpinnerStatus="primary" nbSpinnerSize="large">
  <div class="modal-header">
    <div class="d-flex">
      <h6 class="modal-title ModalBody">{{ title }}</h6>
    </div>
    <button type="button" class="close" aria-label="Close" (click)="onHide(false)">
      <span aria-hidden="true"><em class="fa-solid fa-xmark fa-xl"></em></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="isNotesViewMode">
    <div class="row align-items-center">
      <div class="col-12 mb-2">
        <h6>Note Name</h6>
        <nb-card
          ><p>{{ notesEntityDetails.noteName }}</p></nb-card
        >
      </div>
      <div class="col-12 summary-field mb-2">
        <h6>Summary</h6>
        <nb-card
          ><p>{{ notesEntityDetails.summary }}</p></nb-card
        >
      </div>
      <div class="col-12 mb-2">
        <h6 for="noteTagsApply">Note Tags</h6>
        <nb-card>
          <p class="wrapper-notes">
            <ng-container *ngIf="notesEntityDetails?.tagsName?.length">
              <span class="tag-info-badge fw-bold" *ngFor="let tagName of notesEntityDetails?.tagsName | slice : 0 : 5">
                <span class="px-2">
                  {{ tagName }}
                </span>
              </span>
              <span class="tag-info-badge fw-bold" nbTooltipStatus="primary" [nbTooltip]="getTagList(notesEntityDetails?.tagsName)">
                {{ notesEntityDetails?.tagsName?.length > 5 ? '+' + (notesEntityDetails?.tagsName?.length - 5) + ' More' : '' }}
              </span>
            </ng-container>
            <ng-container *ngIf="!notesEntityDetails?.tagsName?.length">N/A</ng-container>
          </p>
        </nb-card>
      </div>
    </div>
    <div class="modal-footer">
      <div class="footer-actions">
        <button nbButton status="secondary" size="medium" type="button" class="float-end m-1" (click)="onHide(false)">Okay</button>
      </div>
    </div>
  </div>
  <div class="modal-body" *ngIf="!isNotesViewMode">
    <form
      name="notesAddEditForm"
      #notesAddEditForm="ngForm"
      aria-labelledby="title"
      autocomplete="off"
      (ngSubmit)="onSubmitNote(notesAddEditForm)"
    >
      <div class="row align-items-center">
        <div class="col-12">
          <label class="label">Note Name<span class="ms-1 text-danger">*</span></label>
          <input
            nbInput
            fullWidth
            name="noteName"
            required
            [(ngModel)]="notesEntityDetails.noteName"
            #noteNameModel="ngModel"
            id="noteName"
            maxlength="255"
          />
          <sfl-error-msg [control]="noteNameModel" fieldName="Note Name"></sfl-error-msg>
        </div>
        <div class="col-12">
          <label class="label">Summary</label>
          <textarea
            nbInput
            fullWidth
            name="noteSummary"
            id="input-noteSummary"
            #noteSummaryModel="ngModel"
            [(ngModel)]="notesEntityDetails.summary"
            maxlength="4000"
            class="note-summary"
          >
          </textarea>
          <small>{{ notesEntityDetails.summary.length }}/4000</small>
        </div>
        <div class="col-12">
          <label class="label" for="noteTagsApply">Note Tags </label>
          <ng-select
            name="noteTagsApply"
            id="note-tags-drop-down"
            class="sfl-track-dropdown"
            [multiple]="true"
            [items]="noteTagList"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="notesEntityDetails.tags"
            #fileTagsApply="ngModel"
            notFoundText="No Note Tags Found"
            placeholder="Select Note Tags"
            [closeOnSelect]="false"
            (search)="onFilter($event)"
            (close)="filteredAppliedTags = []"
          >
            <ng-template ng-header-tmp *ngIf="noteTagList && noteTagList.length">
              <button type="button" (click)="toggleSelectUnselectAllTags(true)" class="btn btn-sm btn-primary me-2">Select all</button>
              <button type="button" (click)="toggleSelectUnselectAllTags(false)" class="btn btn-sm btn-primary ml-2">Unselect all</button>
            </ng-template>
            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
              <input id="item-{{ index }}" type="checkbox" [ngModel]="item$.selected" name="item-{{ index }}" />
              {{ item.name }}
            </ng-template>
            <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
              <div class="ng-value" *ngFor="let item of items | slice : 0 : 2">
                <span class="ng-value-label">{{ item.name }}</span>
                <span class="ng-value-icon right" (click)="clear(item)" aria-hidden="true">×</span>
              </div>
              <div class="ng-value" *ngIf="items.length > 2">
                <span class="ng-value-label">+{{ items.length - 2 }} </span>
              </div>
            </ng-template>
          </ng-select>
        </div>
      </div>
      <div class="modal-footer">
        <div class="footer-actions">
          <button nbButton status="secondary" size="medium" type="button" class="float-end m-1" (click)="onHide(false)">Cancel</button>
          <button nbButton status="primary" size="medium" type="submit" class="float-end m-1">{{ title }}</button>
        </div>
      </div>
    </form>
  </div>
</div>
