import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Subscription } from 'rxjs';
import { ConfirmDialogComponent } from '../../../../@shared/components/confirm-dialog/confirm-dialog.component';
import { MessageVM } from '../../../../@shared/models/messageVM.model';
import { MasterReportModel, SiteAuditNewJhaModel } from '../../../../@shared/models/report.model';
import { AlertService } from '../../../../@shared/services';
import { CommonService } from '../../../../@shared/services/common.service';
import { JhaPreviewComponent } from '../../../safety/jha/jha-add-edit/jha-preview/jha-preview.component';
import { JhaService } from '../../../safety/jha/jha.service';
import { jhaDetail, JhaThreeClickItem, JhaThreeEventDataClass } from '../../../ticket-management/ticket.model';
import * as uuid from 'uuid';
import { StorageService } from '../../../../@shared/services/storage.service';

@Component({
  selector: 'sfl-jhathree',
  templateUrl: './jhathree.component.html',
  styleUrls: ['./jhathree.component.scss']
})
export class JhathreeComponent implements OnInit, OnChanges {
  @Input() jhaDatas: MasterReportModel;
  @Input() jhaList: jhaDetail[] = [];
  jhaForm: FormGroup;
  jhaListDropdownForm: FormGroup = new FormGroup({
    jhaListDropdown: new FormControl()
  });
  selectedJha: string[] = [];
  loading = false;
  modalRef: BsModalRef;
  subscription: Subscription = new Subscription();
  workorderId: number;
  clonedJHAList: jhaDetail[] = [];
  @Input() isFinal: boolean;
  @Input() reportType: String;
  @Input() jhaListLoader: boolean = false;
  @Input() isCPSValueModified!: {
    [JhaThreeClickItem.CREATE_NEW_JHA_BTN]: boolean;
    [JhaThreeClickItem.JHA_SELECTION_DROPDOWN_OPEN]: boolean;
  };
  @Output() reportDataModified: EventEmitter<boolean> = new EventEmitter(false);
  @Output() jhaThreeEvents: EventEmitter<JhaThreeEventDataClass> = new EventEmitter();

  constructor(
    private readonly fb: FormBuilder,
    private readonly jhaService: JhaService,
    private readonly alertService: AlertService,
    private readonly route: ActivatedRoute,
    private readonly modalService: BsModalService,
    private readonly commonService: CommonService,
    private readonly router: Router,
    private readonly storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.jhaForm = this.fb.group({
      jhaMap: this.fb.array([])
    });
    this.route.params.subscribe(params => {
      this.reportType = params.type;
      if (params && Number(params.id)) {
        this.workorderId = params.id;
        this.isFinal = params.isFinal;
      }
    });
    this.setJhaListForListing();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['jhaListLoader'] && changes['jhaList']) {
      if (this.reportType === 'siteauditreport') {
        this.setJhaListForListing();
      }
    }
  }

  setJhaListForListing(): void {
    this.clonedJHAList = [...(this.jhaList ?? [])];
    this.setJHADatas();
    this.setJHAList();
  }

  setJHADatas(): void {
    this.jhaDatas[0].jhaMap = this.jhaDatas[0].jhaMap.map(item => ({ ...item, isNewAdded: false }));
    this.reportDataModified.emit(true);
  }

  setJHAList(): void {
    const usedJHAList = this.jhaDatas[0].jhaMap.map(item => item.id);
    this.clonedJHAList = this.jhaList?.filter(item => !usedJHAList.includes(item.id));
  }

  jhaDropDownOpen(): void {
    if (this.isCPSValueModified && this.isCPSValueModified[JhaThreeClickItem.JHA_SELECTION_DROPDOWN_OPEN]) {
      this.jhaThreeEmitEvents(JhaThreeClickItem.JHA_SELECTION_DROPDOWN_OPEN);
    }
  }

  jhaThreeEmitEvents(clickedItem: JhaThreeClickItem, isClicked: boolean = true): void {
    const jhaThreeEventData = new JhaThreeEventDataClass(clickedItem, isClicked);
    this.jhaThreeEvents.emit(jhaThreeEventData);
  }

  addJha() {
    const form = this.fb.group({
      id: ['', Validators.required]
    });
    this.jhaMap.push(form);
  }

  get jhaMap() {
    return this.jhaForm.controls['jhaMap'] as FormArray;
  }

  onJhaChange(event, i) {
    // to be used
    // this.jhaMap.at(i).patchValue(event);
    // this.jhaDatas[0].jhaMap = this.jhaMap.value;
    const newJHAValue = { ...event, isNewAdded: true };
    if (this.reportType === 'siteauditreport') {
      this.jhaDatas[0].jhaMap[0] = newJHAValue;
    } else {
      this.jhaDatas[0].jhaMap.push(newJHAValue);
    }
  }

  jhaListDropdownFormClear(): void {
    if (this.jhaListDropdownForm) {
      this.jhaListDropdownForm.controls.jhaListDropdown.reset();
    }
  }

  deleteJha(id) {
    // to be used
    // this.jhaMap.removeAt(i);
    this.jhaDatas[0].jhaMap = this.jhaDatas[0].jhaMap.filter(item => item.id !== id);
  }

  downloadJhaReport(id) {
    this.loading = true;
    this.jhaService.generateJhaPdfReport(id).subscribe({
      next: data => {
        this.jhaService.downloadJhaPdfReport(id).subscribe({
          next: data => {
            if (data) {
              const link = this.commonService.createObject(data, 'application/pdf');
              const finalReportName = name;
              link.download = finalReportName + '.pdf';
              link.click();
              this.loading = false;
            } else {
              this.loading = false;
            }
          }
        });
      },
      error: e => {
        this.loading = false;
      }
    });
  }

  deleteJhaUsingAPI(id) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      initialState: { message: 'Are you sure want to delete this JHA?' }
    };
    this.modalRef = this.modalService.show(ConfirmDialogComponent, ngModalOptions);
    this.modalRef.content.onClose.subscribe(result => {
      if (result) {
        this.loading = true;
        const modal = {
          reportId: id,
          ticketWoId: Number(this.workorderId),
          isTicket: false
        };
        this.subscription.add(
          this.jhaService.deleteWorkOrderTicket(modal).subscribe({
            next: (res: MessageVM) => {
              if (res) {
                this.deleteJha(id);
                this.alertService.showSuccessToast(res.message);
                this.loading = false;
              } else {
                this.loading = false;
              }
            }
          })
        );
      }
    });
  }

  navigateJhaAddEditPage(mode, jhaGuid = uuid.v4()) {
    if (mode === 'add' && this.reportType === 'siteauditreport') {
      if (this.isCPSValueModified && this.isCPSValueModified[JhaThreeClickItem.CREATE_NEW_JHA_BTN]) {
        this.jhaThreeEmitEvents(JhaThreeClickItem.CREATE_NEW_JHA_BTN);
        return;
      }
      const { customerName, portfolioName, siteName, reportGuid } = this.jhaDatas[0];
      const isSiteAuditJHA = this.reportType === 'siteauditreport';
      const siteAuditNewJhaModel = new SiteAuditNewJhaModel({
        jhaGuid,
        customerName,
        portfolioName,
        siteName,
        reportId: reportGuid,
        isSiteAuditJHA
      });
      this.storageService.set('siteAuditNewJhaModel', siteAuditNewJhaModel);
    }
    this.router.navigate(['entities', 'safety', 'site-audit-jha', 'upload', mode, jhaGuid]);
  }

  previewJHA(reportId, jha) {
    const ngModalOptions: ModalOptions = {
      backdrop: 'static',
      keyboard: false,
      animated: true,
      class: 'modal-xl modal-dialog-right',
      initialState: { jhaId: reportId, jha: jha }
    };
    this.modalRef = this.modalService.show(JhaPreviewComponent, ngModalOptions);
  }
}
