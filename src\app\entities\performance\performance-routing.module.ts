import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from '../../@shared/services/permission.guard';
import { QE_MENU_MODULE_ENUM } from '../../@shared/enums/qe-menu.enum';
import { QEAnalyticsGatheringGuard } from '../qe-analytics/services/qe-analytics-gathering.guard';

const routes: Routes = [
  {
    path: 'data-table',
    loadChildren: () => import('./data-table/data-table.module').then(m => m.DataTableModule),
    canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
    data: {
      permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PER_DATA_TABLE,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PERFORMANCE
    }
  },
  {
    path: 'report',
    loadChildren: () => import('./report/report.module').then(m => m.ReportModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PER_REPORTS,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PERFORMANCE
    }
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PER_DASHBOARD,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PERFORMANCE
    }
  },
  {
    path: 'power-chart',
    loadChildren: () => import('./power-chart/power-chart.module').then(m => m.PowerChartModule),
    canActivate: [QEAnalyticsGatheringGuard],
    data: {
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PERFORMANCE
    }
  },
  {
    path: 'outage',
    loadChildren: () => import('./outage/outage.module').then(m => m.OutageModule),
    canActivate: [PermissionGuard, QEAnalyticsGatheringGuard],
    data: {
      permittedRoles: ['admin', 'portfolioManager', 'commercialAssetsManager', 'fieldTech', 'analyst'],
      qeAnalyticsMenuItem: QE_MENU_MODULE_ENUM.PER_ALERTS,
      qeAnalyticsParentItem: QE_MENU_MODULE_ENUM.PERFORMANCE
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PerformanceRoutingModule {}
